{"name": "personality-clone-frontend", "version": "1.0.0", "description": "Frontend for 100% Personality Cloning System", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "d3": "^7.8.5", "marked": "^9.1.6", "highlight.js": "^11.9.0", "dayjs": "^1.11.10"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "@types/d3": "^7.4.3"}, "engines": {"node": ">=16.0.0"}}