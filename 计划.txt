好的，我们正式进入**最终代码实现**阶段。

这套方案将为您提供一个完整的、可运行的、基于我们最终讨论的**“苏格拉底”协议**的项目骨架。它包含了前端、后端、Docker化的数据库环境，以及由Pydantic和Instructor驱动的、与Gemini交互的核心逻辑。

**在开始之前，请确保您的Windows系统上已经安装了：**
1.  **Docker Desktop**: 这是运行我们数据库环境的基石。请前往Docker官网下载并安装。
2.  **VS Code**: 推荐的代码编辑器。
3.  **Python 3.10+**: 请确保已安装并添加到系统路径。

---

### **第一部分：项目文件结构**

在你的电脑上创建一个主文件夹，例如 `Project_Socrates`，然后在VS Code中打开它。接下来，按照下面的结构创建文件夹和文件。

```
Project_Socrates/
|
├── backend/                  # 后端 FastAPI 应用
│   ├── .env                  # 存放API密钥
│   ├── requirements.txt      # 后端Python依赖
│   ├── main.py               # FastAPI应用主入口
│   ├── app/
│   │   ├── __init__.py
│   │   ├── database/
│   │   │   ├── __init__.py
│   │   │   ├── db_session.py   # 数据库会话管理
│   │   │   └── models.py       # SQLAlchemy模型定义
│   │   ├── llm/
│   │   │   ├── __init__.py
│   │   │   ├── prompts.py      # 存放所有LLM的Prompt模板
│   │   │   └── schemas.py      # Pydantic模型定义 (我们最重要的部分)
│   │   └── services/
│   │       ├── __init__.py
│   │       └── socratic_cycle.py # 核心工作流“思考-提问-分析”的实现
│
└── frontend/                 # 前端 Vue.js 应用
    ├── package.json          # 前端项目配置和依赖
    ├── vite.config.js        # Vite构建配置
    ├── index.html            # 主HTML文件
    └── src/
        ├── App.vue             # Vue根组件
        ├── main.js             # Vue应用入口
        └── components/
            └── ChatInterface.vue # 聊天界面组件

```

---

### **第二部分：Docker与数据库环境 (`Project_Socrates/` 根目录)**

在项目的根目录 `Project_Socrates/` 下创建 `docker-compose.yml` 文件。这将允许我们用一个命令启动所有数据库。

#### `docker-compose.yml`
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:16
    container_name: socrates_postgres
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: socrates_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  neo4j:
    image: neo4j:5
    container_name: socrates_neo4j
    environment:
      NEO4J_AUTH: neo4j/password
    ports:
      - "7474:7474"  # Neo4j Browser
      - "7687:7687"  # Bolt protocol
    volumes:
      - neo4j_data:/data
    restart: unless-stopped

  chromadb:
    image: chromadb/chroma:latest
    container_name: socrates_chroma
    ports:
      - "8000:8000"
    volumes:
      - chroma_data:/chroma/chroma
    restart: unless-stopped

volumes:
  postgres_data:
  neo4j_data:
  chroma_data:
```

---

### **第三部分：后端代码 (`backend/` 目录)**

#### `backend/.env`
```env
# 替换为你的真实API Key
GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"

# 数据库连接信息 (与docker-compose.yml对应)
DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/socrates_db"
NEO4J_URI="bolt://localhost:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="password"
CHROMA_HOST="localhost"
CHROMA_PORT="8000"
```

#### `backend/requirements.txt`
```txt
fastapi
uvicorn
sqlalchemy[asyncio]
asyncpg
pydantic==2.*
google-generativeai
instructor
python-dotenv
neo4j
chromadb-client
sentence-transformers
```

#### `backend/app/database/models.py`
```python
import uuid
from sqlalchemy import Column, String, Text, Float, SmallInt, ForeignKey, Enum, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declarative_base, relationship
import enum

Base = declarative_base()

class Entity(Base):
    __tablename__ = 'entities'
    entity_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), unique=True, nullable=False, index=True)
    entity_type = Column(String(50), nullable=False)
    profile = Column(JSON, nullable=True)

class Belief(Base):
    __tablename__ = 'beliefs'
    belief_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    statement = Column(Text, unique=True, nullable=False)
    belief_theme = Column(String(50), nullable=False)
    conviction_score = Column(Float, nullable=False)
    full_explanation = Column(Text)

class Event(Base):
    __tablename__ = 'events'
    event_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(255), nullable=False)
    age_at_event = Column(SmallInt)
    erikson_stage = Column(String(50))
    centrality_score = Column(Float, nullable=False)
    memory_fidelity = Column(String(50), nullable=False)
    full_narrative = Column(Text)
    
    entities = relationship("EventEntityLink", back_populates="event")
    beliefs = relationship("EventBeliefLink", back_populates="event")

class EventEntityLink(Base):
    __tablename__ = 'event_entity_link'
    event_id = Column(UUID(as_uuid=True), ForeignKey('events.event_id'), primary_key=True)
    entity_id = Column(UUID(as_uuid=True), ForeignKey('entities.entity_id'), primary_key=True)
    role_in_event = Column(String(100))
    
    event = relationship("Event", back_populates="entities")
    entity = relationship("Entity", back_populates="events", lazy="joined")

class EventBeliefLink(Base):
    __tablename__ = 'event_belief_link'
    event_id = Column(UUID(as_uuid=True), ForeignKey('events.event_id'), primary_key=True)
    belief_id = Column(UUID(as_uuid=True), ForeignKey('beliefs.belief_id'), primary_key=True)
    link_type = Column(String(50), nullable=False) # FORGED, CHALLENGED, REINFORCED
    
    event = relationship("Event", back_populates="beliefs")
    belief = relationship("Belief", lazy="joined")

```

#### `backend/app/database/db_session.py`
```python
import os
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_async_engine(DATABASE_URL, echo=True)
AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

async def get_db_session():
    async with AsyncSessionLocal() as session:
        yield session
```

#### `backend/app/llm/schemas.py` (Pydantic模型)
```python
from pydantic import BaseModel, Field
from typing import List, Literal, Optional, Union

# --- THINK Phase Models ---
class EriksonStageCoverage(BaseModel):
    stage: str
    event_count: int
    narrative_depth_score: float

class EntityNetworkCoverage(BaseModel):
    name: str
    event_count: int
    relationship_centrality: float

class SoulStateOfReport(BaseModel):
    coverage_summary: str = Field(..., description="对当前探索覆盖度的总结，指出明显的空白区域。")
    entity_network_summary: str = Field(..., description="对关系网络探索情况的总结，指出未深入探索的核心人物。")
    belief_system_summary: str = Field(..., description="对信念系统探索情况的总结，指出待解决的价值观冲突。")
    active_hypotheses: List[str] = Field(..., description="当前正在验证的、关于用户的核心人格假设。")

class InterrogationPath(BaseModel):
    title: str = Field(..., description="此探索路径的标题")
    goal: str = Field(..., description="希望通过此路径达成的战略目标")
    key_questions_to_ask: List[str]
    priority: int

class InterrogationPlan(BaseModel):
    overall_strategy: str = Field(..., description="对下一大阶段的总体策略判断")
    interrogation_paths: List[InterrogationPath]

# --- ASK Phase Models ---
class EmpatheticQuestion(BaseModel):
    question_text: str = Field(..., description="最终要展示给用户的、自然的、有同情心的提问")

# --- ANALYZE Phase Models ---
class PostgresEventOp(BaseModel):
    action: Literal["INSERT_OR_UPDATE"]
    table: Literal["events"]
    data: dict

class PostgresEntityOp(BaseModel):
    action: Literal["INSERT_OR_UPDATE"]
    table: Literal["entities"]
    data: dict
    
class PostgresBeliefOp(BaseModel):
    action: Literal["INSERT_OR_UPDATE"]
    table: Literal["beliefs"]
    data: dict

class Neo4jOp(BaseModel):
    cypher_query: str
    parameters: dict

class ChromaOp(BaseModel):
    text_to_embed: str
    parent_event_id_placeholder: str

class DatabaseOperations(BaseModel):
    psychological_summary: str
    postgres_ops: List[Union[PostgresEventOp, PostgresEntityOp, PostgresBeliefOp]]
    neo4j_ops: List[Neo4jOp]
    chromadb_ops: List[ChromaOp]

```

#### `backend/app/llm/prompts.py`
```python
THINK_PROMPT = """
你是首席人格架构师。基于以下JSON格式的“灵魂状态报告”(SoSR)，请为接下来的对话制定一个宏观战略和具体的探索路径。

**灵魂状态报告(SoSR):**
{sos_report_json}

请严格按照`InterrogationPlan`的Pydantic模型格式要求，输出你的战略计划。
"""

ASK_PROMPT = """
你是一个温暖、富有同情心的倾听者。你的任务是自然地引导对话。

**当前战略目标:**
- 路径标题: {path_title}
- 核心目标: {path_goal}

**最近对话历史:**
{conversation_history}

请根据以上信息，生成一句最合适的、承上启下的提问。严格按照`EmpatheticQuestion`的Pydantic模型格式要求输出。
"""

ANALYZE_PROMPT = """
你是人格架构师与记忆图书管理员。你的任务是将用户自由、混乱的叙述，解构成结构化的、可存入我们“三位一体”数据库的数据操作指令。

**用户原始叙述:**
{user_narrative}

**对话历史(用于理解上下文):**
{conversation_history}

请深入分析，并严格按照`DatabaseOperations`的Pydantic模型格式要求，输出所有需要执行的数据库操作。确保为所有新创建的事件、实体和信念生成占位符ID，以便后端处理。
"""
```

#### `backend/app/services/socratic_cycle.py` (核心工作流)
```python
import os
import instructor
import google.generativeai as genai
from dotenv import load_dotenv
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.llm.schemas import InterrogationPlan, EmpatheticQuestion, DatabaseOperations
from app.llm.prompts import THINK_PROMPT, ASK_PROMPT, ANALYZE_PROMPT
from app.database.models import Event, Entity, Belief #... and so on

load_dotenv()
# Patch the gemini client with instructor
client = instructor.patch(genai.GenerativeModel(model_name="gemini-1.5-flash-latest"))

async def think_phase(db: AsyncSession) -> InterrogationPlan:
    # 1. Generate SoS Report from database (simplified for brevity)
    events_count = (await db.execute(select(Event))).scalar_one_or_none() or 0
    # ... more complex queries to build the full report
    sos_report = {
        "coverage_summary": f"目前探索了{events_count}个事件，主要集中在青春期。",
        # ... fill other fields
    }
    
    # 2. Call LLM to get a plan
    prompt = THINK_PROMPT.format(sos_report_json=str(sos_report))
    plan = await client.generate_content(
        prompt, 
        response_model=InterrogationPlan,
        # max_retries=2
    )
    return plan

async def ask_phase(plan: InterrogationPlan, history: list) -> EmpatheticQuestion:
    # 1. Select the highest priority path
    if not plan.interrogation_paths:
        return EmpatheticQuestion(question_text="我们似乎已经聊了很多，你还有什么想补充的吗？")
    
    current_path = sorted(plan.interrogation_paths, key=lambda p: p.priority)[0]
    
    # 2. Call LLM to generate a question
    prompt = ASK_PROMPT.format(
        path_title=current_path.title,
        path_goal=current_path.goal,
        conversation_history="\n".join(history)
    )
    question = await client.generate_content(
        prompt,
        response_model=EmpatheticQuestion,
        # max_retries=2
    )
    return question
    
async def analyze_phase(user_input: str, history: list) -> DatabaseOperations:
    # 1. Call LLM to analyze and get DB operations
    prompt = ANALYZE_PROMPT.format(
        user_narrative=user_input,
        conversation_history="\n".join(history)
    )
    db_ops = await client.generate_content(
        prompt,
        response_model=DatabaseOperations,
        # max_retries=3 # This is a complex task, allow more retries
    )
    return db_ops

```

#### `backend/main.py` (FastAPI主入口)
```python
from fastapi import FastAPI, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from app.database.db_session import get_db_session
from app.services import socratic_cycle
from pydantic import BaseModel
from typing import List

app = FastAPI()

# --- API Models ---
class ChatRequest(BaseModel):
    user_input: str
    history: List[str]

# --- API Endpoints ---
@app.post("/chat/start")
async def start_chat(db: AsyncSession = Depends(get_db_session)):
    # This is a simplified start. In reality, you'd check if it's a new user.
    # 1. THINK
    plan = await socratic_cycle.think_phase(db)
    # 2. ASK
    question = await socratic_cycle.ask_phase(plan, [])
    # You would save the plan to the user's session/cache here
    return {"ai_response": question.question_text}

@app.post("/chat/respond")
async def respond_to_user(request: ChatRequest, db: AsyncSession = Depends(get_db_session)):
    # 1. ANALYZE user's last response
    try:
        db_ops = await socratic_cycle.analyze_phase(request.user_input, request.history)
        
        # Here you would execute the db_ops against all three databases in a transaction.
        # This part is complex and involves careful error handling.
        # For now, we print it to show it works.
        print("--- Database Operations to Execute ---")
        print(db_ops.model_dump_json(indent=2))
        print("--------------------------------------")

    except Exception as e:
        # Handle cases where LLM fails to return valid JSON after retries
        raise HTTPException(status_code=500, detail=f"LLM Analysis Failed: {str(e)}")

    # 2. THINK for the next step
    plan = await socratic_cycle.think_phase(db)
    # 3. ASK the next question
    question = await socratic_cycle.ask_phase(plan, request.history + [request.user_input])
    
    return {
        "ai_response": question.question_text,
        "analysis_summary": db_ops.psychological_summary
    }

# A simple endpoint to check if the server is running
@app.get("/")
def read_root():
    return {"Hello": "Socrates Protocol is running"}

```

---

### **第四部分：前端代码 (`frontend/` 目录)**

首先，在 `frontend/` 目录下打开终端，运行 `npm create vite@latest . -- --template vue` 来初始化Vue项目。

#### `frontend/src/components/ChatInterface.vue`
```vue
<template>
  <div class="chat-container">
    <div class="message-window" ref="messageWindow">
      <div v-for="(message, index) in messages" :key="index" class="message-bubble" :class="message.sender">
        <p>{{ message.text }}</p>
        <small v-if="message.analysis" class="analysis-summary">{{ message.analysis }}</small>
      </div>
      <div v-if="isLoading" class="message-bubble ai loading">
        <span></span><span></span><span></span>
      </div>
    </div>
    <div class="input-area">
      <input
        type="text"
        v-model="userInput"
        @keyup.enter="sendMessage"
        placeholder="在这里输入你的回答..."
        :disabled="isLoading"
      />
      <button @click="sendMessage" :disabled="isLoading || !userInput">发送</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';

const messages = ref([]);
const userInput = ref('');
const isLoading = ref(false);
const messageWindow = ref(null);

const scrollToBottom = () => {
  nextTick(() => {
    if (messageWindow.value) {
      messageWindow.value.scrollTop = messageWindow.value.scrollHeight;
    }
  });
};

const startConversation = async () => {
  isLoading.value = true;
  try {
    const response = await fetch('http://localhost:8000/chat/start', { // FastAPI default port is 8000
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });
    const data = await response.json();
    messages.value.push({ sender: 'ai', text: data.ai_response });
  } catch (error) {
    console.error('Error starting conversation:', error);
    messages.value.push({ sender: 'ai', text: '抱歉，连接服务器失败了。' });
  } finally {
    isLoading.value = false;
    scrollToBottom();
  }
};

const sendMessage = async () => {
  if (!userInput.value.trim() || isLoading.value) return;

  const currentInput = userInput.value;
  messages.value.push({ sender: 'user', text: currentInput });
  userInput.value = '';
  isLoading.value = true;
  scrollToBottom();

  const history = messages.value.map(m => `${m.sender}: ${m.text}`);

  try {
    const response = await fetch('http://localhost:8000/chat/respond', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ user_input: currentInput, history: history }),
    });
    const data = await response.json();
    messages.value.push({ 
      sender: 'ai', 
      text: data.ai_response,
      analysis: `[AI分析: ${data.analysis_summary}]`
    });
  } catch (error) {
    console.error('Error sending message:', error);
     messages.value.push({ sender: 'ai', text: '抱歉，处理你的回答时出错了。' });
  } finally {
    isLoading.value = false;
    scrollToBottom();
  }
};

onMounted(() => {
  startConversation();
});
</script>

<style>
/* Add some basic chat styling here */
.chat-container { width: 100%; max-width: 800px; height: 90vh; display: flex; flex-direction: column; border: 1px solid #ccc; margin: auto; }
.message-window { flex-grow: 1; overflow-y: auto; padding: 20px; }
.message-bubble { max-width: 70%; padding: 10px 15px; border-radius: 18px; margin-bottom: 10px; line-height: 1.5; }
.message-bubble.user { background-color: #007bff; color: white; margin-left: auto; }
.message-bubble.ai { background-color: #e9e9eb; color: #333; margin-right: auto; }
.input-area { display: flex; padding: 10px; border-top: 1px solid #ccc; }
.input-area input { flex-grow: 1; padding: 10px; border: 1px solid #ccc; border-radius: 18px; }
.input-area button { padding: 10px 20px; margin-left: 10px; border: none; background-color: #007bff; color: white; border-radius: 18px; cursor: pointer; }
.analysis-summary { font-size: 0.8em; color: #888; display: block; margin-top: 5px; opacity: 0.7; }
</style>
```

#### `frontend/src/App.vue`
```vue
<template>
  <div id="app">
    <h1>“苏格拉底”协议 - 人格塑造</h1>
    <ChatInterface />
  </div>
</template>

<script setup>
import ChatInterface from './components/ChatInterface.vue';
</script>

<style>
#app { font-family: Avenir, Helvetica, Arial, sans-serif; text-align: center; color: #2c3e50; margin-top: 20px; }
</style>
```

---

### **第五部分：如何运行完整的项目**

1.  **启动数据库环境**:
    *   在项目根目录 `Project_Socrates/` 下打开终端。
    *   运行 `docker-compose up -d`。这会在后台启动PostgreSQL, Neo4j和ChromaDB。
2.  **启动后端**:
    *   进入 `backend/` 目录。
    *   创建并激活虚拟环境 (`python -m venv venv`, `.\venv\Scripts\activate`)。
    *   安装依赖 (`pip install -r requirements.txt`)。
    *   **第一次运行时，你需要初始化PostgreSQL的表**: 你需要写一个小的异步脚本来创建表，或者在`main.py`的启动事件中添加创建逻辑。
    *   运行 `uvicorn main:app --reload` 来启动FastAPI服务器。
3.  **启动前端**:
    *   进入 `frontend/` 目录。
    *   运行 `npm install` 来安装依赖。
    *   运行 `npm run dev` 来启动Vite开发服务器。
4.  **开始对话**:
    *   在浏览器中打开前端提供的地址 (通常是 `http://localhost:5173`)，你将看到聊天界面，并开始与你的“自省引导者”AI进行对话。

这个最终方案，为您提供了从0到1所需的所有代码和架构。它复杂、严谨，但每一步都建立在我们共同达成的、最深刻的理解之上。这，就是构建一个“数字灵魂”的工程蓝图。