#!/usr/bin/env python3
"""
快速启动脚本 - 用于开发和测试
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_docker():
    """检查Docker是否可用"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker已就绪")
            return True
        else:
            print("❌ Docker未安装或不可用")
            return False
    except FileNotFoundError:
        print("❌ Docker未安装")
        return False

def start_databases():
    """启动数据库服务"""
    print("🔄 启动数据库服务...")
    try:
        result = subprocess.run(['docker-compose', 'up', '-d'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 数据库服务启动成功")
            return True
        else:
            print(f"❌ 数据库服务启动失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 启动数据库服务时出错: {e}")
        return False

def wait_for_services():
    """等待服务启动"""
    print("⏳ 等待服务启动...")
    
    services = {
        'PostgreSQL': 'http://localhost:5432',
        'Neo4j': 'http://localhost:7474',
        'ChromaDB': 'http://localhost:8000/api/v1/heartbeat',
        'Redis': 'redis://localhost:6379'
    }
    
    # 简单等待
    time.sleep(15)
    print("✅ 服务启动等待完成")

def check_python_deps():
    """检查Python依赖"""
    backend_dir = Path('backend')
    requirements_file = backend_dir / 'requirements.txt'
    
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False
    
    print("📦 检查Python依赖...")
    
    # 检查虚拟环境
    venv_dir = backend_dir / 'venv'
    if not venv_dir.exists():
        print("🔄 创建虚拟环境...")
        try:
            subprocess.run([sys.executable, '-m', 'venv', str(venv_dir)], check=True)
            print("✅ 虚拟环境创建成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 创建虚拟环境失败: {e}")
            return False
    
    # 确定pip路径
    if os.name == 'nt':  # Windows
        pip_path = venv_dir / 'Scripts' / 'pip.exe'
        python_path = venv_dir / 'Scripts' / 'python.exe'
    else:  # Unix/Linux/macOS
        pip_path = venv_dir / 'bin' / 'pip'
        python_path = venv_dir / 'bin' / 'python'
    
    # 安装依赖
    print("🔄 安装Python依赖...")
    try:
        subprocess.run([str(pip_path), 'install', '-r', str(requirements_file)], check=True)
        print("✅ Python依赖安装成功")
        return True, python_path
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装Python依赖失败: {e}")
        return False, None

def setup_env():
    """设置环境变量"""
    backend_dir = Path('backend')
    env_file = backend_dir / '.env'
    env_example = backend_dir / '.env.example'
    
    if not env_file.exists() and env_example.exists():
        print("🔄 创建环境变量文件...")
        try:
            with open(env_example, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换一些默认值
            content = content.replace('YOUR_GEMINI_API_KEY_HERE', 'demo-key-please-replace')
            
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 环境变量文件创建成功")
            print("⚠️  请编辑 backend/.env 文件，填入真实的API密钥")
        except Exception as e:
            print(f"❌ 创建环境变量文件失败: {e}")

def start_backend(python_path):
    """启动后端服务"""
    print("🔄 启动后端服务...")
    backend_dir = Path('backend')
    
    try:
        # 初始化数据库
        print("🔄 初始化数据库...")
        subprocess.run([str(python_path), 'init_db.py'], cwd=backend_dir, check=True)
        print("✅ 数据库初始化成功")
        
        # 启动FastAPI服务
        print("🚀 启动FastAPI服务...")
        print("📍 后端服务将在 http://localhost:8000 启动")
        print("📖 API文档: http://localhost:8000/docs")
        
        # 在新的进程中启动服务
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                str(python_path), '-m', 'uvicorn', 'main:app', 
                '--reload', '--host', '0.0.0.0', '--port', '8000'
            ], cwd=backend_dir, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Unix/Linux/macOS
            subprocess.Popen([
                str(python_path), '-m', 'uvicorn', 'main:app', 
                '--reload', '--host', '0.0.0.0', '--port', '8000'
            ], cwd=backend_dir)
        
        return True
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return False

def check_node_deps():
    """检查Node.js依赖"""
    frontend_dir = Path('frontend')
    package_json = frontend_dir / 'package.json'
    
    if not package_json.exists():
        print("❌ package.json 文件不存在")
        return False
    
    node_modules = frontend_dir / 'node_modules'
    if not node_modules.exists():
        print("🔄 安装Node.js依赖...")
        try:
            subprocess.run(['npm', 'install'], cwd=frontend_dir, check=True)
            print("✅ Node.js依赖安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装Node.js依赖失败: {e}")
            return False
    
    return True

def start_frontend():
    """启动前端服务"""
    print("🔄 启动前端服务...")
    frontend_dir = Path('frontend')
    
    try:
        print("🚀 启动Vue开发服务器...")
        print("📍 前端服务将在 http://localhost:5173 启动")
        
        # 在新的进程中启动服务
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd=frontend_dir, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Unix/Linux/macOS
            subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd=frontend_dir)
        
        return True
    except Exception as e:
        print(f"❌ 启动前端服务失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 100% 人格复刻系统 - 快速启动")
    print("=" * 50)
    print()
    
    # 检查Docker
    if not check_docker():
        print("请先安装并启动Docker Desktop")
        return
    
    # 启动数据库
    if not start_databases():
        print("数据库启动失败，请检查Docker配置")
        return
    
    # 等待服务启动
    wait_for_services()
    
    # 设置环境变量
    setup_env()
    
    # 检查并安装Python依赖
    result = check_python_deps()
    if isinstance(result, tuple):
        success, python_path = result
        if not success:
            print("Python环境配置失败")
            return
    else:
        print("Python环境配置失败")
        return
    
    # 启动后端
    if not start_backend(python_path):
        print("后端启动失败")
        return
    
    # 等待后端启动
    print("⏳ 等待后端服务启动...")
    time.sleep(5)
    
    # 检查并安装Node.js依赖
    if not check_node_deps():
        print("Node.js环境配置失败")
        return
    
    # 启动前端
    if not start_frontend():
        print("前端启动失败")
        return
    
    print()
    print("=" * 50)
    print("🎉 系统启动完成！")
    print("=" * 50)
    print()
    print("📊 服务地址:")
    print("  🌐 前端界面: http://localhost:5173")
    print("  🔧 后端API: http://localhost:8000")
    print("  📚 API文档: http://localhost:8000/docs")
    print("  🗄️  Neo4j浏览器: http://localhost:7474")
    print()
    print("🔑 演示账号:")
    print("  用户名: demo")
    print("  密码: demo123")
    print()
    print("⚠️  重要提醒:")
    print("  1. 请编辑 backend/.env 文件，填入真实的Gemini API密钥")
    print("  2. 首次使用需要注册账号或使用演示账号")
    print("  3. 按 Ctrl+C 可以停止服务")
    print()
    print("📖 详细文档请查看 README.md")

if __name__ == "__main__":
    main()
