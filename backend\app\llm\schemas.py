from pydantic import BaseModel, Field
from typing import List, Literal, Optional, Union, Dict, Any
from enum import Enum

# === Core Personality Analysis Models ===

class PersonalityDimension(BaseModel):
    dimension: str = Field(..., description="人格维度名称")
    score: float = Field(..., ge=0, le=1, description="该维度的得分 (0-1)")
    confidence: float = Field(..., ge=0, le=1, description="评估的置信度")
    evidence: List[str] = Field(..., description="支持这个评分的证据")

class CognitiveStyle(BaseModel):
    primary_style: str = Field(..., description="主要认知风格")
    secondary_style: Optional[str] = Field(None, description="次要认知风格")
    decision_speed: float = Field(..., ge=0, le=1, description="决策速度 (0=慢, 1=快)")
    risk_tolerance: float = Field(..., ge=0, le=1, description="风险承受度")
    analytical_depth: float = Field(..., ge=0, le=1, description="分析深度偏好")

class EmotionalProfile(BaseModel):
    emotional_stability: float = Field(..., ge=0, le=1, description="情绪稳定性")
    expressiveness: float = Field(..., ge=0, le=1, description="情感表达度")
    empathy_level: float = Field(..., ge=0, le=1, description="共情能力")
    dominant_emotions: List[str] = Field(..., description="主导情绪类型")
    stress_responses: List[str] = Field(..., description="压力反应模式")

class CommunicationStyle(BaseModel):
    formality_level: float = Field(..., ge=0, le=1, description="正式程度")
    directness: float = Field(..., ge=0, le=1, description="直接程度")
    verbosity: float = Field(..., ge=0, le=1, description="话语量")
    emotional_tone: str = Field(..., description="情感基调")
    preferred_topics: List[str] = Field(..., description="偏好话题")
    avoidance_topics: List[str] = Field(..., description="回避话题")

# === Deep Analysis Models ===

class PersonalityInsight(BaseModel):
    insight_type: str = Field(..., description="洞察类型")
    description: str = Field(..., description="洞察描述")
    confidence: float = Field(..., ge=0, le=1, description="置信度")
    supporting_evidence: List[str] = Field(..., description="支持证据")
    implications: List[str] = Field(..., description="行为含义")

class BehaviorPattern(BaseModel):
    pattern_name: str = Field(..., description="行为模式名称")
    trigger_conditions: List[str] = Field(..., description="触发条件")
    typical_response: str = Field(..., description="典型反应")
    frequency: str = Field(..., description="出现频率")
    context_dependency: str = Field(..., description="情境依赖性")

class ValueSystem(BaseModel):
    core_values: List[str] = Field(..., description="核心价值观")
    value_hierarchy: List[str] = Field(..., description="价值观层次")
    moral_framework: str = Field(..., description="道德框架")
    ethical_boundaries: List[str] = Field(..., description="伦理边界")
    value_conflicts: List[str] = Field(..., description="价值观冲突")

# === Prediction Models ===

class SituationalResponse(BaseModel):
    situation: str = Field(..., description="情境描述")
    predicted_response: str = Field(..., description="预测反应")
    confidence: float = Field(..., ge=0, le=1, description="预测置信度")
    reasoning: str = Field(..., description="预测推理")
    alternative_responses: List[str] = Field(..., description="可能的替代反应")

class DecisionPattern(BaseModel):
    decision_type: str = Field(..., description="决策类型")
    decision_factors: List[str] = Field(..., description="决策因素")
    weight_distribution: Dict[str, float] = Field(..., description="因素权重分布")
    typical_outcome: str = Field(..., description="典型结果")
    decision_speed: str = Field(..., description="决策速度")

# === Comprehensive Analysis Models ===

class PersonalitySnapshot(BaseModel):
    """完整的人格快照"""
    target_name: str = Field(..., description="目标人物姓名")
    analysis_timestamp: str = Field(..., description="分析时间戳")
    completion_percentage: float = Field(..., ge=0, le=100, description="分析完成度百分比")

    # Core personality
    big_five: List[PersonalityDimension] = Field(..., description="大五人格维度")
    cognitive_style: CognitiveStyle = Field(..., description="认知风格")
    emotional_profile: Dict[str, Any] = Field(..., description="情感档案")
    communication_style: Dict[str, Any] = Field(..., description="沟通风格")

    # Deep insights
    personality_insights: List[PersonalityInsight] = Field(..., description="人格洞察")
    behavior_patterns: List[BehaviorPattern] = Field(..., description="行为模式")
    value_system: ValueSystem = Field(..., description="价值体系")

    # Predictive elements
    decision_patterns: List[DecisionPattern] = Field(..., description="决策模式")
    stress_responses: List[str] = Field(..., description="压力反应")
    growth_areas: List[str] = Field(..., description="成长领域")

# === Analysis Phase Models ===

class DeepAnalysisResult(BaseModel):
    """深度分析结果"""
    psychological_summary: str = Field(..., description="心理学总结")
    personality_updates: PersonalitySnapshot = Field(..., description="人格更新")
    new_insights: List[PersonalityInsight] = Field(..., description="新发现的洞察")
    confidence_score: float = Field(..., ge=0, le=1, description="整体分析置信度")
    
    # Database operations
    postgres_operations: List[Dict[str, Any]] = Field(..., description="PostgreSQL操作")
    neo4j_operations: List[Dict[str, Any]] = Field(..., description="Neo4j操作")
    chromadb_operations: List[Dict[str, Any]] = Field(..., description="ChromaDB操作")

# === Question Generation Models ===

class StrategicQuestion(BaseModel):
    """战略性问题"""
    question_text: str = Field(..., description="问题文本")
    question_type: str = Field(..., description="问题类型")
    target_insight: str = Field(..., description="目标洞察")
    expected_depth: str = Field(..., description="期望深度")
    follow_up_potential: bool = Field(..., description="是否有后续问题潜力")

class QuestioningStrategy(BaseModel):
    """提问策略"""
    overall_goal: str = Field(..., description="总体目标")
    current_focus: str = Field(..., description="当前焦点")
    priority_areas: List[str] = Field(..., description="优先领域")
    strategic_questions: List[StrategicQuestion] = Field(..., description="战略问题")
    session_plan: str = Field(..., description="会话计划")

# === Similarity Assessment Models ===

class SimilarityMetric(BaseModel):
    """相似度指标"""
    metric_name: str = Field(..., description="指标名称")
    score: float = Field(..., ge=0, le=1, description="相似度得分")
    weight: float = Field(..., ge=0, le=1, description="指标权重")
    details: str = Field(..., description="详细说明")

class PersonalitySimilarity(BaseModel):
    """人格相似度评估"""
    overall_similarity: float = Field(..., ge=0, le=1, description="总体相似度")
    dimension_similarities: List[SimilarityMetric] = Field(..., description="维度相似度")
    behavioral_similarity: float = Field(..., ge=0, le=1, description="行为相似度")
    communication_similarity: float = Field(..., ge=0, le=1, description="沟通相似度")
    value_alignment: float = Field(..., ge=0, le=1, description="价值观一致性")
    
    strengths: List[str] = Field(..., description="相似性优势")
    gaps: List[str] = Field(..., description="差距领域")
    improvement_suggestions: List[str] = Field(..., description="改进建议")

# === Legacy Models (for compatibility) ===

class InterrogationPlan(BaseModel):
    overall_strategy: str = Field(..., description="总体策略")
    questioning_strategy: QuestioningStrategy = Field(..., description="提问策略")

class EmpatheticQuestion(BaseModel):
    question_text: str = Field(..., description="共情问题文本")
    question_context: str = Field(..., description="问题背景")
    expected_response_type: str = Field(..., description="期望回应类型")
