#!/usr/bin/env python3
"""
停止所有服务的脚本
"""

import subprocess
import sys
import os

def stop_docker_services():
    """停止Docker服务"""
    print("🔄 停止数据库服务...")
    try:
        result = subprocess.run(['docker-compose', 'down'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 数据库服务已停止")
            return True
        else:
            print(f"❌ 停止数据库服务失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 停止数据库服务时出错: {e}")
        return False

def kill_processes():
    """终止相关进程"""
    print("🔄 终止应用进程...")
    
    if os.name == 'nt':  # Windows
        # 终止uvicorn进程
        try:
            subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], capture_output=True)
            subprocess.run(['taskkill', '/F', '/IM', 'uvicorn.exe'], capture_output=True)
        except:
            pass
        
        # 终止node进程
        try:
            subprocess.run(['taskkill', '/F', '/IM', 'node.exe'], capture_output=True)
        except:
            pass
    else:  # Unix/Linux/macOS
        # 终止uvicorn进程
        try:
            subprocess.run(['pkill', '-f', 'uvicorn'], capture_output=True)
        except:
            pass
        
        # 终止node进程
        try:
            subprocess.run(['pkill', '-f', 'vite'], capture_output=True)
        except:
            pass
    
    print("✅ 进程终止完成")

def main():
    """主函数"""
    print("=" * 40)
    print("🛑 停止人格复刻系统服务")
    print("=" * 40)
    print()
    
    # 停止Docker服务
    stop_docker_services()
    
    # 终止进程
    kill_processes()
    
    print()
    print("✅ 所有服务已停止")

if __name__ == "__main__":
    main()
