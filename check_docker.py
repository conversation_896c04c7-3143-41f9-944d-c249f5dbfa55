#!/usr/bin/env python3
"""
Docker状态检查和修复脚本
"""

import subprocess
import time
import sys
import os

def check_docker_installed():
    """检查Docker是否安装"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Docker已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ Docker未正确安装")
            return False
    except FileNotFoundError:
        print("❌ Docker未安装，请先安装Docker Desktop")
        return False

def check_docker_running():
    """检查Docker是否运行"""
    try:
        result = subprocess.run(['docker', 'info'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker服务正在运行")
            return True
        else:
            print("❌ Docker服务未运行")
            print(f"错误信息: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 检查Docker状态时出错: {e}")
        return False

def start_docker_desktop():
    """尝试启动Docker Desktop"""
    print("🔄 尝试启动Docker Desktop...")
    
    if os.name == 'nt':  # Windows
        try:
            # 尝试启动Docker Desktop
            subprocess.Popen([
                'C:\\Program Files\\Docker\\Docker\\Docker Desktop.exe'
            ], shell=True)
            print("✅ 已尝试启动Docker Desktop")
            return True
        except Exception as e:
            print(f"❌ 启动Docker Desktop失败: {e}")
            return False
    else:
        print("💡 请手动启动Docker Desktop应用")
        return False

def wait_for_docker():
    """等待Docker启动"""
    print("⏳ 等待Docker启动...")
    
    max_wait = 60  # 最多等待60秒
    wait_time = 0
    
    while wait_time < max_wait:
        if check_docker_running():
            return True
        
        print(f"   等待中... ({wait_time}/{max_wait}秒)")
        time.sleep(5)
        wait_time += 5
    
    print("❌ Docker启动超时")
    return False

def check_docker_compose():
    """检查docker-compose是否可用"""
    try:
        result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Docker Compose可用: {result.stdout.strip()}")
            return True
        else:
            # 尝试使用docker compose (新版本)
            result = subprocess.run(['docker', 'compose', 'version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Docker Compose可用: {result.stdout.strip()}")
                return True
            else:
                print("❌ Docker Compose不可用")
                return False
    except FileNotFoundError:
        print("❌ Docker Compose未安装")
        return False

def test_docker_functionality():
    """测试Docker基本功能"""
    print("🧪 测试Docker基本功能...")
    
    try:
        # 运行hello-world容器
        result = subprocess.run([
            'docker', 'run', '--rm', 'hello-world'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Docker功能测试通过")
            return True
        else:
            print(f"❌ Docker功能测试失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Docker功能测试超时")
        return False
    except Exception as e:
        print(f"❌ Docker功能测试出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🐳 Docker状态检查和修复")
    print("=" * 50)
    print()
    
    # 1. 检查Docker是否安装
    if not check_docker_installed():
        print("\n💡 解决方案:")
        print("1. 下载并安装Docker Desktop:")
        print("   https://www.docker.com/products/docker-desktop/")
        print("2. 安装完成后重新运行此脚本")
        return
    
    print()
    
    # 2. 检查Docker是否运行
    if not check_docker_running():
        print("\n🔄 尝试修复Docker服务...")
        
        # 尝试启动Docker Desktop
        if start_docker_desktop():
            # 等待Docker启动
            if wait_for_docker():
                print("✅ Docker服务已启动")
            else:
                print("\n💡 手动解决方案:")
                print("1. 手动启动Docker Desktop应用")
                print("2. 等待Docker完全启动（系统托盘图标变绿）")
                print("3. 重新运行此脚本")
                return
        else:
            print("\n💡 手动解决方案:")
            print("1. 手动启动Docker Desktop应用")
            print("2. 如果启动失败，尝试重启计算机")
            print("3. 检查Windows功能中的Hyper-V是否启用")
            return
    
    print()
    
    # 3. 检查Docker Compose
    if not check_docker_compose():
        print("\n💡 Docker Compose解决方案:")
        print("1. 更新Docker Desktop到最新版本")
        print("2. 或使用 'docker compose' 命令替代 'docker-compose'")
        return
    
    print()
    
    # 4. 测试Docker功能
    if test_docker_functionality():
        print("\n🎉 Docker环境完全正常！")
        print("\n🚀 现在可以启动项目服务:")
        print("   docker-compose up -d")
        print("   或运行: python quick_start.py")
    else:
        print("\n⚠️  Docker环境有问题，建议:")
        print("1. 重启Docker Desktop")
        print("2. 重启计算机")
        print("3. 重新安装Docker Desktop")

if __name__ == "__main__":
    main()
