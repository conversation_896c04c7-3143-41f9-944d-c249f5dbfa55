"""
数据库初始化脚本
"""

import asyncio
import os
from sqlalchemy.ext.asyncio import create_async_engine
from dotenv import load_dotenv

from app.database.models import Base
from app.database.db_session import db_manager

load_dotenv()

async def init_database():
    """初始化数据库表"""
    try:
        print("🔄 正在初始化数据库...")
        
        # 创建所有表
        await db_manager.create_tables()
        
        print("✅ 数据库表创建成功")
        
        # 这里可以添加初始数据
        print("📊 数据库初始化完成")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(init_database())
