"""
Database session management with connection pooling and error handling
"""

import os
import asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import Null<PERSON>ool
from sqlalchemy import event
from dotenv import load_dotenv
import structlog

from .models import Base

load_dotenv()

logger = structlog.get_logger()

class DatabaseManager:
    def __init__(self):
        self.database_url = os.getenv("DATABASE_URL")
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable is required")
        
        # Create async engine with optimized settings
        self.engine = create_async_engine(
            self.database_url,
            echo=os.getenv("DEBUG", "false").lower() == "true",
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True,
            pool_recycle=3600,  # Recycle connections every hour
            connect_args={
                "server_settings": {
                    "application_name": "personality_clone_backend",
                }
            }
        )
        
        # Create session factory
        self.async_session_factory = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Add connection event listeners
        self._setup_event_listeners()
    
    def _setup_event_listeners(self):
        """Setup database event listeners for monitoring"""
        
        @event.listens_for(self.engine.sync_engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            # This is for PostgreSQL, but we can add connection optimizations here
            pass
        
        @event.listens_for(self.engine.sync_engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            logger.debug("Database connection checked out")
        
        @event.listens_for(self.engine.sync_engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            logger.debug("Database connection checked in")

    async def create_tables(self):
        """Create all database tables"""
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error("Failed to create database tables", error=str(e))
            raise

    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session with proper error handling"""
        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                logger.error("Database session error", error=str(e))
                raise
            finally:
                await session.close()

    async def health_check(self) -> bool:
        """Check database connectivity"""
        try:
            async with self.async_session_factory() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False

    async def close(self):
        """Close database connections"""
        await self.engine.dispose()
        logger.info("Database connections closed")

# Global database manager instance
db_manager = DatabaseManager()

# Dependency for FastAPI
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency for database sessions"""
    async for session in db_manager.get_session():
        yield session

# Utility functions for database operations
async def execute_with_retry(session: AsyncSession, operation, max_retries: int = 3):
    """Execute database operation with retry logic"""
    for attempt in range(max_retries):
        try:
            result = await operation(session)
            await session.commit()
            return result
        except Exception as e:
            await session.rollback()
            if attempt == max_retries - 1:
                logger.error(
                    "Database operation failed after retries",
                    error=str(e),
                    attempts=max_retries
                )
                raise
            else:
                logger.warning(
                    "Database operation failed, retrying",
                    error=str(e),
                    attempt=attempt + 1
                )
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

class TransactionManager:
    """Context manager for database transactions across multiple operations"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.savepoint = None
    
    async def __aenter__(self):
        self.savepoint = await self.session.begin_nested()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            await self.savepoint.rollback()
            logger.error("Transaction rolled back", error=str(exc_val))
        else:
            await self.savepoint.commit()
            logger.debug("Transaction committed successfully")

# Database initialization function
async def init_database():
    """Initialize database with tables and basic data"""
    try:
        await db_manager.create_tables()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error("Database initialization failed", error=str(e))
        raise

# Cleanup function
async def cleanup_database():
    """Cleanup database connections"""
    await db_manager.close()
