#!/usr/bin/env python3
"""
项目完整性检查脚本
"""

import os
from pathlib import Path

def check_file_exists(file_path, description=""):
    """检查文件是否存在"""
    if Path(file_path).exists():
        print(f"✅ {file_path} {description}")
        return True
    else:
        print(f"❌ {file_path} {description} - 文件缺失")
        return False

def check_directory_exists(dir_path, description=""):
    """检查目录是否存在"""
    if Path(dir_path).is_dir():
        print(f"✅ {dir_path}/ {description}")
        return True
    else:
        print(f"❌ {dir_path}/ {description} - 目录缺失")
        return False

def main():
    """主检查函数"""
    print("=" * 60)
    print("🔍 100% 人格复刻系统 - 项目完整性检查")
    print("=" * 60)
    print()
    
    total_checks = 0
    passed_checks = 0
    
    # 根目录文件
    print("📁 根目录文件:")
    files_to_check = [
        ("README.md", "项目说明文档"),
        ("docker-compose.yml", "Docker服务配置"),
        ("quick_start.py", "快速启动脚本"),
        ("stop_services.py", "停止服务脚本"),
        ("test_system.py", "系统测试脚本"),
        ("check_project.py", "项目检查脚本"),
        ("DEPLOYMENT_CHECKLIST.md", "部署检查清单"),
        ("start.bat", "Windows启动脚本"),
        ("start.sh", "Linux/macOS启动脚本"),
    ]
    
    for file_path, description in files_to_check:
        total_checks += 1
        if check_file_exists(file_path, description):
            passed_checks += 1
    
    print()
    
    # 后端目录结构
    print("🔧 后端目录结构:")
    backend_files = [
        ("backend/main.py", "FastAPI应用入口"),
        ("backend/requirements.txt", "Python依赖"),
        ("backend/init_db.py", "数据库初始化脚本"),
        ("backend/.env.example", "环境变量示例"),
        ("backend/app/__init__.py", "应用包初始化"),
        ("backend/app/database/__init__.py", "数据库模块"),
        ("backend/app/database/models.py", "数据库模型"),
        ("backend/app/database/db_session.py", "数据库会话管理"),
        ("backend/app/llm/__init__.py", "LLM模块"),
        ("backend/app/llm/schemas.py", "Pydantic模型"),
        ("backend/app/llm/prompts.py", "提示词模板"),
        ("backend/app/services/__init__.py", "服务模块"),
        ("backend/app/services/personality_analyzer.py", "人格分析服务"),
        ("backend/app/services/enhanced_socratic_cycle.py", "增强苏格拉底循环"),
        ("backend/sql/init.sql", "SQL初始化脚本"),
    ]
    
    for file_path, description in backend_files:
        total_checks += 1
        if check_file_exists(file_path, description):
            passed_checks += 1
    
    print()
    
    # 前端目录结构
    print("🎨 前端目录结构:")
    frontend_files = [
        ("frontend/package.json", "Node.js项目配置"),
        ("frontend/vite.config.js", "Vite构建配置"),
        ("frontend/index.html", "HTML入口文件"),
        ("frontend/.env.example", "前端环境变量示例"),
        ("frontend/src/main.js", "Vue应用入口"),
        ("frontend/src/App.vue", "Vue根组件"),
        ("frontend/src/router/index.js", "路由配置"),
        ("frontend/src/stores/auth.js", "认证状态管理"),
        ("frontend/src/stores/app.js", "应用状态管理"),
        ("frontend/src/utils/api.js", "API工具类"),
    ]
    
    for file_path, description in frontend_files:
        total_checks += 1
        if check_file_exists(file_path, description):
            passed_checks += 1
    
    print()
    
    # 前端组件
    print("🧩 前端组件:")
    component_files = [
        ("frontend/src/components/layout/AppHeader.vue", "应用头部组件"),
        ("frontend/src/components/layout/AppSidebar.vue", "侧边栏组件"),
        ("frontend/src/components/common/GlobalNotifications.vue", "全局通知组件"),
    ]
    
    for file_path, description in component_files:
        total_checks += 1
        if check_file_exists(file_path, description):
            passed_checks += 1
    
    print()
    
    # 前端页面
    print("📄 前端页面:")
    page_files = [
        ("frontend/src/views/Dashboard.vue", "仪表板页面"),
        ("frontend/src/views/auth/Login.vue", "登录页面"),
        ("frontend/src/views/auth/Register.vue", "注册页面"),
        ("frontend/src/views/personality/PersonalityList.vue", "人格列表页面"),
        ("frontend/src/views/personality/PersonalityDetail.vue", "人格详情页面"),
        ("frontend/src/views/personality/PersonalityCreate.vue", "创建人格页面"),
        ("frontend/src/views/chat/ChatInterface.vue", "对话界面"),
        ("frontend/src/views/prediction/PredictionLab.vue", "预测实验室"),
        ("frontend/src/views/validation/ValidationCenter.vue", "验证中心"),
        ("frontend/src/views/analytics/Analytics.vue", "数据分析页面"),
        ("frontend/src/views/settings/Settings.vue", "设置页面"),
        ("frontend/src/views/error/NotFound.vue", "404页面"),
    ]
    
    for file_path, description in page_files:
        total_checks += 1
        if check_file_exists(file_path, description):
            passed_checks += 1
    
    print()
    
    # 目录结构检查
    print("📂 目录结构:")
    directories = [
        ("backend", "后端目录"),
        ("backend/app", "应用目录"),
        ("backend/app/database", "数据库模块"),
        ("backend/app/llm", "LLM模块"),
        ("backend/app/services", "服务模块"),
        ("backend/sql", "SQL脚本目录"),
        ("frontend", "前端目录"),
        ("frontend/src", "前端源码"),
        ("frontend/src/components", "组件目录"),
        ("frontend/src/components/layout", "布局组件"),
        ("frontend/src/components/common", "通用组件"),
        ("frontend/src/views", "页面目录"),
        ("frontend/src/views/auth", "认证页面"),
        ("frontend/src/views/personality", "人格页面"),
        ("frontend/src/views/chat", "对话页面"),
        ("frontend/src/views/prediction", "预测页面"),
        ("frontend/src/views/validation", "验证页面"),
        ("frontend/src/views/analytics", "分析页面"),
        ("frontend/src/views/settings", "设置页面"),
        ("frontend/src/views/error", "错误页面"),
        ("frontend/src/stores", "状态管理"),
        ("frontend/src/utils", "工具函数"),
        ("frontend/src/router", "路由配置"),
    ]
    
    for dir_path, description in directories:
        total_checks += 1
        if check_directory_exists(dir_path, description):
            passed_checks += 1
    
    print()
    
    # 检查结果
    print("=" * 60)
    print("📊 检查结果统计")
    print("=" * 60)
    
    success_rate = (passed_checks / total_checks) * 100
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"完整度: {success_rate:.1f}%")
    
    print()
    
    if success_rate >= 95:
        print("🎉 项目结构完整，可以开始部署！")
        print()
        print("🚀 下一步操作:")
        print("  1. 运行 python quick_start.py 快速启动")
        print("  2. 或者按照 DEPLOYMENT_CHECKLIST.md 手动部署")
        print("  3. 运行 python test_system.py 测试系统")
    elif success_rate >= 80:
        print("⚠️  项目基本完整，但有一些文件缺失")
        print("建议检查缺失的文件后再进行部署")
    else:
        print("❌ 项目结构不完整，请检查缺失的文件")
        print("建议重新下载或克隆项目")
    
    print()
    print("📖 详细部署说明请查看:")
    print("  - README.md")
    print("  - DEPLOYMENT_CHECKLIST.md")

if __name__ == "__main__":
    main()
