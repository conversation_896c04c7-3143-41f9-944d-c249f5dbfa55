<template>
  <aside class="app-sidebar" :class="{ collapsed: sidebarCollapsed }">
    <div class="sidebar-content">
      <el-menu
        :default-active="activeMenu"
        :collapse="sidebarCollapsed"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <el-menu-item
          v-for="route in menuRoutes"
          :key="route.path"
          :index="route.path"
          @click="handleMenuClick(route)"
        >
          <el-icon>
            <component :is="route.meta.icon" />
          </el-icon>
          <template #title>{{ route.meta.title }}</template>
        </el-menu-item>
      </el-menu>
    </div>
    
    <!-- 侧边栏底部 -->
    <div class="sidebar-footer">
      <el-button
        type="text"
        class="collapse-button"
        @click="toggleSidebar"
      >
        <el-icon>
          <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
        </el-icon>
        <span v-if="!sidebarCollapsed">收起菜单</span>
      </el-button>
    </div>
  </aside>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { Expand, Fold } from '@element-plus/icons-vue'

import { useAppStore } from '../../stores/app'
import { menuRoutes } from '../../router'

const route = useRoute()
const appStore = useAppStore()

// 计算属性
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
const activeMenu = computed(() => {
  // 根据当前路由确定激活的菜单项
  const path = route.path
  
  // 精确匹配
  if (menuRoutes.find(r => r.path === path)) {
    return path
  }
  
  // 模糊匹配（用于子路由）
  for (const menuRoute of menuRoutes) {
    if (path.startsWith(menuRoute.path) && menuRoute.path !== '/') {
      return menuRoute.path
    }
  }
  
  return '/'
})

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const handleMenuClick = (route) => {
  // 可以在这里添加菜单点击的额外逻辑
  console.log('菜单点击:', route.meta.title)
}
</script>

<style scoped>
.app-sidebar {
  position: fixed;
  left: 0;
  top: 60px;
  bottom: 0;
  width: 250px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s ease;
  z-index: 999;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.05);
}

.app-sidebar.collapsed {
  width: 64px;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.sidebar-menu :deep(.el-menu-item) {
  height: 56px;
  line-height: 56px;
  padding: 0 20px;
  margin: 0 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.sidebar-menu :deep(.el-menu-item:hover) {
  background-color: #f0f9ff;
  color: #409eff;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.sidebar-menu :deep(.el-menu-item.is-active .el-icon) {
  color: #fff;
}

.sidebar-menu :deep(.el-menu-item .el-icon) {
  margin-right: 12px;
  font-size: 18px;
}

.sidebar-menu.el-menu--collapse :deep(.el-menu-item) {
  padding: 0 20px;
  text-align: center;
}

.sidebar-menu.el-menu--collapse :deep(.el-menu-item .el-icon) {
  margin-right: 0;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

.collapse-button {
  width: 100%;
  justify-content: flex-start;
  color: #606266;
  font-size: 14px;
}

.collapse-button:hover {
  color: #409eff;
  background-color: #f0f9ff;
}

.app-sidebar.collapsed .collapse-button {
  justify-content: center;
}

.app-sidebar.collapsed .collapse-button span {
  display: none;
}

/* 自定义滚动条 */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .app-sidebar:not(.collapsed) {
    transform: translateX(0);
  }
  
  /* 移动端遮罩 */
  .app-sidebar:not(.collapsed)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }
}

/* 深色模式 */
.dark .app-sidebar {
  background: #1f2937;
  border-right-color: #374151;
}

.dark .sidebar-menu :deep(.el-menu-item) {
  color: #d1d5db;
}

.dark .sidebar-menu :deep(.el-menu-item:hover) {
  background-color: #374151;
  color: #60a5fa;
}

.dark .sidebar-footer {
  border-top-color: #374151;
}

.dark .collapse-button {
  color: #d1d5db;
}

.dark .collapse-button:hover {
  color: #60a5fa;
  background-color: #374151;
}
</style>
