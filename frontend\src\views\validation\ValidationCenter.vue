<template>
  <div class="validation-center">
    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><CircleCheck /></el-icon>
        <h2>验证中心</h2>
        <p>此功能正在开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { CircleCheck } from '@element-plus/icons-vue'
</script>

<style scoped>
.validation-center {
  padding: 20px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h2 {
  margin: 20px 0 10px;
}
</style>
