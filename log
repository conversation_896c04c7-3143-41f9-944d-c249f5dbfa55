
Personal

clone-plan
E:\cursor\agent\clone-plan
chromadb

chromadb/chroma:latest
8000:8000⁠
redis

redis:7-alpine
6379:6379
neo4j

neo4j:5
7474:7474⁠
postgres

postgres:16
5432:5432⁠
elasticsearch

elasticsearch/elasticsearch:8.11.0
9200:9200⁠

{"@timestamp":"2025-06-19T08:49:15.317Z", "log.level": "INFO", "message":"adding component template [behavioral_analytics-events-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.324Z", "log.level": "INFO", "message":"adding index template [.ml-notifications-000002] for index patterns [.ml-notifications-000002]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.327Z", "log.level": "INFO", "message":"adding index template [.ml-state] for index patterns [.ml-state*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.332Z", "log.level": "INFO", "message":"adding component template [elastic-connectors-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.349Z", "log.level": "INFO", "message":"adding index template [.monitoring-es-mb] for index patterns [.monitoring-es-8-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.361Z", "log.level": "INFO", "message":"adding index template [.ml-anomalies-] for index patterns [.ml-anomalies-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.363Z", "log.level": "INFO", "message":"adding component template [elastic-connectors-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.376Z", "log.level": "INFO", "message":"adding index template [.monitoring-beats-mb] for index patterns [.monitoring-beats-8-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.381Z", "log.level": "INFO", "message":"adding index template [.ml-stats] for index patterns [.ml-stats-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.384Z", "log.level": "INFO", "message":"adding component template [metrics-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.388Z", "log.level": "INFO", "message":"adding component template [synthetics-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.393Z", "log.level": "INFO", "message":"adding component template [metrics-tsdb-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.399Z", "log.level": "INFO", "message":"adding component template [data-streams-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.402Z", "log.level": "INFO", "message":"adding component template [logs-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.405Z", "log.level": "INFO", "message":"adding component template [ecs@dynamic_templates]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.409Z", "log.level": "INFO", "message":"adding component template [synthetics-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.413Z", "log.level": "INFO", "message":"adding component template [metrics-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.419Z", "log.level": "INFO", "message":"adding index template [.slm-history] for index patterns [.slm-history-5*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.423Z", "log.level": "INFO", "message":"adding index template [.kibana-reporting] for index patterns [.kibana-reporting*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.427Z", "log.level": "INFO", "message":"adding index template [ilm-history] for index patterns [ilm-history-5*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.430Z", "log.level": "INFO", "message":"adding component template [.deprecation-indexing-mappings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.433Z", "log.level": "INFO", "message":"adding component template [.deprecation-indexing-settings]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}

{"@timestamp":"2025-06-19T08:49:15.436Z", "log.level": "INFO", "message":"adding index template [.fleet-fileds-fromhost-meta] for index patterns [.fleet-fileds-fromhost-meta-*]", "ecs.version": "1.2.0","service.name":"ES_ECS","event.dataset":"elasticsearch.server","process.thread.name":"elasticsearch[634ba011d17e][masterService#updateTask][T#1]","log.logger":"org.elasticsearch.cluster.metadata.MetadataIndexTemplateService","elasticsearch.cluster.uuid":"eUeaBZsBQGy867eKfFlZ7Q","elasticsearch.node.id":"J9nVq7t_T6e6zeBhF6sZtg","elasticsearch.node.name":"634ba011d17e","elasticsearch.cluster.name":"docker-cluster"}


