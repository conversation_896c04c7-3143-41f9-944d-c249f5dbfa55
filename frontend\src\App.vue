<template>
  <div id="app">
    <el-config-provider :locale="locale">
      <!-- 全局加载遮罩 -->
      <el-loading
        v-loading="globalLoading"
        element-loading-text="正在处理中..."
        element-loading-background="rgba(0, 0, 0, 0.8)"
        element-loading-spinner="el-icon-loading"
      >
        <!-- 主要内容区域 -->
        <div class="app-container">
          <!-- 顶部导航栏 -->
          <AppHeader v-if="showHeader" />
          
          <!-- 侧边栏 -->
          <AppSidebar v-if="showSidebar" />
          
          <!-- 主内容区 -->
          <main class="main-content" :class="{ 'with-sidebar': showSidebar }">
            <router-view v-slot="{ Component, route }">
              <transition name="fade" mode="out-in">
                <component :is="Component" :key="route.path" />
              </transition>
            </router-view>
          </main>
          
          <!-- 全局通知 -->
          <GlobalNotifications />
        </div>
      </el-loading>
    </el-config-provider>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import AppHeader from './components/layout/AppHeader.vue'
import AppSidebar from './components/layout/AppSidebar.vue'
import GlobalNotifications from './components/common/GlobalNotifications.vue'

import { useAuthStore } from './stores/auth'
import { useAppStore } from './stores/app'

// 状态管理
const authStore = useAuthStore()
const appStore = useAppStore()
const route = useRoute()

// 响应式数据
const locale = ref(zhCn)
const globalLoading = computed(() => appStore.globalLoading)

// 计算属性
const showHeader = computed(() => {
  return authStore.isAuthenticated && !route.meta.hideHeader
})

const showSidebar = computed(() => {
  return authStore.isAuthenticated && !route.meta.hideSidebar
})

// 生命周期
onMounted(async () => {
  // 初始化应用
  await initializeApp()
})

// 监听路由变化
watch(route, (to) => {
  // 更新页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 人格复刻系统`
  }
})

// 方法
const initializeApp = async () => {
  try {
    appStore.setGlobalLoading(true)
    
    // 检查认证状态
    await authStore.checkAuth()
    
    // 初始化应用设置
    await appStore.initializeApp()
    
  } catch (error) {
    console.error('应用初始化失败:', error)
    ElMessage.error('应用初始化失败，请刷新页面重试')
  } finally {
    appStore.setGlobalLoading(false)
  }
}
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.main-content {
  transition: margin-left 0.3s ease;
  min-height: calc(100vh - 60px);
  margin-top: 60px;
  padding: 20px;
}

.main-content.with-sidebar {
  margin-left: 250px;
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content.with-sidebar {
    margin-left: 0;
  }
  
  .main-content {
    padding: 10px;
  }
}

/* 全局样式 */
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-input__inner) {
  border-radius: 6px;
}

/* 自定义滚动条 */
:deep(.el-scrollbar__wrap) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar) {
  width: 6px;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb) {
  background-color: #c1c1c1;
  border-radius: 3px;
}

:deep(.el-scrollbar__wrap::-webkit-scrollbar-thumb:hover) {
  background-color: #a8a8a8;
}
</style>

<style>
/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
               'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

#app {
  height: 100%;
}

/* 自定义主题色 */
:root {
  --el-color-primary: #667eea;
  --el-color-primary-light-3: #8b9df0;
  --el-color-primary-light-5: #a6b5f3;
  --el-color-primary-light-7: #c1cdf6;
  --el-color-primary-light-8: #d1ddf8;
  --el-color-primary-light-9: #e8edfb;
  --el-color-primary-dark-2: #5a72e8;
}

/* 动画效果 */
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.full-width {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
