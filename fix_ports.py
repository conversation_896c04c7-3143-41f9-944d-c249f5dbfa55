#!/usr/bin/env python3
"""
端口冲突检查和修复脚本
"""

import socket
import subprocess
import sys
import os

def check_port(host, port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def find_free_port(start_port, host='localhost'):
    """找到一个可用的端口"""
    port = start_port
    while port < start_port + 100:
        if not check_port(host, port):
            return port
        port += 1
    return None

def kill_process_on_port(port):
    """终止占用端口的进程"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(
                ['netstat', '-ano'], 
                capture_output=True, 
                text=True
            )
            lines = result.stdout.split('\n')
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) > 4:
                        pid = parts[-1]
                        try:
                            subprocess.run(['taskkill', '/F', '/PID', pid], 
                                         capture_output=True)
                            print(f"✅ 已终止占用端口 {port} 的进程 (PID: {pid})")
                            return True
                        except:
                            pass
        else:  # Unix/Linux/macOS
            result = subprocess.run(
                ['lsof', '-ti', f':{port}'], 
                capture_output=True, 
                text=True
            )
            if result.stdout.strip():
                pid = result.stdout.strip()
                try:
                    subprocess.run(['kill', '-9', pid], capture_output=True)
                    print(f"✅ 已终止占用端口 {port} 的进程 (PID: {pid})")
                    return True
                except:
                    pass
    except:
        pass
    return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 端口冲突检查和修复")
    print("=" * 50)
    print()
    
    # 检查的端口列表
    ports_to_check = {
        5432: "PostgreSQL",
        7474: "Neo4j HTTP",
        7687: "Neo4j Bolt", 
        8001: "ChromaDB",
        6380: "Redis",
        9200: "Elasticsearch"
    }
    
    conflicts = []
    
    print("🔍 检查端口占用情况...")
    for port, service in ports_to_check.items():
        if check_port('localhost', port):
            print(f"❌ 端口 {port} ({service}) 被占用")
            conflicts.append((port, service))
        else:
            print(f"✅ 端口 {port} ({service}) 可用")
    
    print()
    
    if not conflicts:
        print("🎉 所有端口都可用，可以启动服务！")
        return
    
    print(f"⚠️  发现 {len(conflicts)} 个端口冲突")
    print()
    
    # 询问用户是否要自动修复
    response = input("是否要自动修复端口冲突？(y/n): ").lower().strip()
    
    if response == 'y':
        print("\n🔄 开始修复端口冲突...")
        
        for port, service in conflicts:
            print(f"\n处理 {service} (端口 {port})...")
            
            # 尝试终止占用进程
            if kill_process_on_port(port):
                continue
            
            # 如果无法终止，寻找替代端口
            new_port = find_free_port(port + 1)
            if new_port:
                print(f"💡 建议将 {service} 端口改为 {new_port}")
                
                # 这里可以添加自动修改配置文件的逻辑
                if service == "Redis":
                    print(f"   请手动修改 docker-compose.yml 中 Redis 的端口映射为 {new_port}:6379")
                elif service == "ChromaDB":
                    print(f"   请手动修改 docker-compose.yml 中 ChromaDB 的端口映射为 {new_port}:8000")
            else:
                print(f"❌ 无法找到 {service} 的替代端口")
    
    print()
    print("🔧 手动解决方案:")
    print("1. 停止占用端口的其他服务")
    print("2. 修改 docker-compose.yml 中的端口映射")
    print("3. 重启计算机释放所有端口")
    print()
    print("📝 常见端口冲突解决:")
    print("- Redis (6379): 可能与本地Redis冲突")
    print("- PostgreSQL (5432): 可能与本地PostgreSQL冲突") 
    print("- ChromaDB (8000): 可能与其他Web服务冲突")
    print()
    print("💡 建议使用以下命令重新启动:")
    print("   docker-compose down")
    print("   docker-compose up -d")

if __name__ == "__main__":
    main()
