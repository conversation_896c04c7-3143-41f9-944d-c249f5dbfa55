# 100% 人格复刻系统

基于AI技术的完整人格复刻与分析系统，通过深度学习和心理学模型实现对目标人物的全方位人格建模。

## 🚀 系统特性

### 核心功能
- **深度人格分析**: 基于大五人格模型和认知心理学理论
- **智能对话引导**: 苏格拉底式提问，逐步深入人格探索
- **行为模式预测**: 预测目标人物在特定情境下的反应
- **相似度评估**: 量化评估人格复刻的准确度
- **多维度建模**: 涵盖认知风格、情感模式、价值体系等

### 技术架构
- **前端**: Vue 3 + Element Plus + Pinia
- **后端**: FastAPI + SQLAlchemy + Pydantic
- **数据库**: PostgreSQL + Neo4j + ChromaDB + Redis
- **AI模型**: Google Gemini + Instructor
- **容器化**: Docker + Docker Compose

## 📋 系统要求

### 必需软件
- Docker Desktop
- Python 3.10+
- Node.js 16+
- Git

### API密钥
- Google Gemini API Key (必需)
- OpenAI API Key (可选)

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd personality-clone-system
```

### 2. 环境配置

#### 后端配置
```bash
cd backend
cp .env.example .env
# 编辑 .env 文件，填入你的API密钥
```

#### 前端配置
```bash
cd frontend
npm install
```

### 3. 启动数据库服务
```bash
# 在项目根目录
docker-compose up -d
```

这将启动以下服务：
- PostgreSQL (端口 5432)
- Neo4j (端口 7474, 7687)
- ChromaDB (端口 8001)
- Redis (端口 6380)
- Elasticsearch (端口 9200)

### 4. 启动后端服务
```bash
cd backend
# 创建虚拟环境
python -m venv venv
# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 启动服务
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 5. 启动前端服务
```bash
cd frontend
npm run dev
```

### 6. 访问系统
- 前端界面: http://localhost:5173
- 后端API: http://localhost:8000
- Neo4j浏览器: http://localhost:7474

## 🔧 开发指南

### 项目结构
```
personality-clone-system/
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── database/       # 数据库模型和会话
│   │   ├── llm/           # LLM相关模块
│   │   └── services/      # 业务逻辑服务
│   ├── main.py            # FastAPI应用入口
│   └── requirements.txt   # Python依赖
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── components/    # Vue组件
│   │   ├── views/         # 页面视图
│   │   ├── stores/        # Pinia状态管理
│   │   └── utils/         # 工具函数
│   └── package.json       # Node.js依赖
└── docker-compose.yml     # 数据库服务配置
```

### 核心工作流程

#### 1. THINK 阶段
- 分析当前人格建模状态
- 识别信息缺口和重点探索领域
- 制定战略性提问计划

#### 2. ASK 阶段
- 基于战略计划生成共情问题
- 自然引导用户深度分享
- 平衡直接询问和间接探索

#### 3. ANALYZE 阶段
- 深度分析用户叙述
- 提取人格特征和行为模式
- 更新数据库中的人格模型

#### 4. PREDICT 阶段
- 基于已建立的人格模型
- 预测特定情境下的反应
- 提供置信度评估

#### 5. VALIDATE 阶段
- 评估人格复刻的准确度
- 识别改进空间
- 提供优化建议

### 数据模型

#### 核心实体
- **PersonalityProfile**: 人格档案主体
- **Entity**: 相关人物、地点、概念
- **Belief**: 信念和价值观
- **Event**: 重要生活事件
- **CognitivePattern**: 认知模式
- **EmotionalResponse**: 情感反应模式

#### 关系建模
- 使用Neo4j存储复杂的关系网络
- PostgreSQL存储结构化数据
- ChromaDB存储向量化的语义信息

## 🧪 测试

### 后端测试
```bash
cd backend
pytest
```

### 前端测试
```bash
cd frontend
npm run test
```

## 📊 监控和日志

系统集成了结构化日志记录：
- 请求/响应日志
- 错误追踪
- 性能监控
- 用户行为分析

## 🔒 安全考虑

- JWT认证机制
- 数据加密存储
- API访问限制
- 隐私保护措施

## 🚧 开发状态

当前版本为初始架构实现，包含：
- ✅ 基础架构搭建
- ✅ 数据库设计
- ✅ API框架
- ✅ 前端界面框架
- 🚧 核心业务逻辑实现中
- 🚧 AI模型集成优化中
- 📋 高级功能规划中

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
1. 查看文档
2. 搜索已有Issues
3. 创建新Issue
4. 联系开发团队

---

**注意**: 这是一个实验性项目，用于研究和教育目的。请确保在使用时遵守相关法律法规和伦理准则。
