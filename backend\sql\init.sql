-- 100% 人格复刻系统数据库初始化脚本
-- 创建必要的扩展和初始数据

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 启用全文搜索扩展
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建枚举类型
DO $$ BEGIN
    CREATE TYPE personality_dimension AS ENUM (
        'openness', 'conscientiousness', 'extraversion', 
        'agreeableness', 'neuroticism'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE cognitive_style AS ENUM (
        'analytical', 'intuitive', 'systematic', 'creative'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE emotional_state AS ENUM (
        'joy', 'sadness', 'anger', 'fear', 'surprise', 'disgust', 'neutral'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建索引函数（在表创建后执行）
CREATE OR REPLACE FUNCTION create_personality_indexes() RETURNS void AS $$
BEGIN
    -- 用户表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_username') THEN
        CREATE INDEX idx_users_username ON users(username);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_email') THEN
        CREATE INDEX idx_users_email ON users(email);
    END IF;

    -- 人格档案表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_personality_profiles_user_id') THEN
        CREATE INDEX idx_personality_profiles_user_id ON personality_profiles(user_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_personality_profiles_target_name') THEN
        CREATE INDEX idx_personality_profiles_target_name ON personality_profiles(target_name);
    END IF;

    -- 实体表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_entities_personality_id') THEN
        CREATE INDEX idx_entities_personality_id ON entities(personality_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_entities_name') THEN
        CREATE INDEX idx_entities_name ON entities(name);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_entities_type') THEN
        CREATE INDEX idx_entities_entity_type ON entities(entity_type);
    END IF;

    -- 信念表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_beliefs_personality_id') THEN
        CREATE INDEX idx_beliefs_personality_id ON beliefs(personality_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_beliefs_category') THEN
        CREATE INDEX idx_beliefs_category ON beliefs(belief_category);
    END IF;

    -- 事件表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_events_personality_id') THEN
        CREATE INDEX idx_events_personality_id ON events(personality_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_events_age') THEN
        CREATE INDEX idx_events_age ON events(age_at_event);
    END IF;

    -- 对话表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_conversations_user_id') THEN
        CREATE INDEX idx_conversations_user_id ON conversations(user_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_conversations_personality_id') THEN
        CREATE INDEX idx_conversations_personality_id ON conversations(personality_id);
    END IF;

    -- 消息表索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_messages_conversation_id') THEN
        CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_messages_timestamp') THEN
        CREATE INDEX idx_messages_timestamp ON messages(timestamp);
    END IF;

    -- 全文搜索索引
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_beliefs_statement_gin') THEN
        CREATE INDEX idx_beliefs_statement_gin ON beliefs USING gin(statement gin_trgm_ops);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_events_narrative_gin') THEN
        CREATE INDEX idx_events_narrative_gin ON events USING gin(full_narrative gin_trgm_ops);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_messages_content_gin') THEN
        CREATE INDEX idx_messages_content_gin ON messages USING gin(content gin_trgm_ops);
    END IF;

END;
$$ LANGUAGE plpgsql;

-- 创建触发器函数用于更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建用于计算人格完成度的函数
CREATE OR REPLACE FUNCTION calculate_personality_completion(profile_id UUID)
RETURNS FLOAT AS $$
DECLARE
    completion_score FLOAT := 0;
    entity_count INTEGER;
    belief_count INTEGER;
    event_count INTEGER;
    cognitive_pattern_count INTEGER;
BEGIN
    -- 计算各个维度的完成度
    
    -- 基础信息 (20%)
    SELECT CASE 
        WHEN target_name IS NOT NULL AND target_name != '' THEN 0.2 
        ELSE 0 
    END INTO completion_score
    FROM personality_profiles 
    WHERE profile_id = calculate_personality_completion.profile_id;
    
    -- 实体关系 (25%)
    SELECT COUNT(*) INTO entity_count
    FROM entities 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(entity_count * 0.05, 0.25);
    
    -- 信念系统 (25%)
    SELECT COUNT(*) INTO belief_count
    FROM beliefs 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(belief_count * 0.08, 0.25);
    
    -- 生活事件 (20%)
    SELECT COUNT(*) INTO event_count
    FROM events 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(event_count * 0.04, 0.20);
    
    -- 认知模式 (10%)
    SELECT COUNT(*) INTO cognitive_pattern_count
    FROM cognitive_patterns 
    WHERE personality_id = calculate_personality_completion.profile_id;
    
    completion_score := completion_score + LEAST(cognitive_pattern_count * 0.02, 0.10);
    
    RETURN LEAST(completion_score * 100, 100);
END;
$$ LANGUAGE plpgsql;

-- 创建用于搜索相似人格的函数
CREATE OR REPLACE FUNCTION find_similar_personalities(
    target_profile_id UUID,
    similarity_threshold FLOAT DEFAULT 0.7
)
RETURNS TABLE(
    profile_id UUID,
    target_name VARCHAR,
    similarity_score FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.profile_id,
        p.target_name,
        (
            1.0 - (
                ABS(p.openness_score - tp.openness_score) +
                ABS(p.conscientiousness_score - tp.conscientiousness_score) +
                ABS(p.extraversion_score - tp.extraversion_score) +
                ABS(p.agreeableness_score - tp.agreeableness_score) +
                ABS(p.neuroticism_score - tp.neuroticism_score)
            ) / 5.0
        ) as similarity_score
    FROM personality_profiles p
    CROSS JOIN personality_profiles tp
    WHERE tp.profile_id = target_profile_id
    AND p.profile_id != target_profile_id
    AND (
        1.0 - (
            ABS(p.openness_score - tp.openness_score) +
            ABS(p.conscientiousness_score - tp.conscientiousness_score) +
            ABS(p.extraversion_score - tp.extraversion_score) +
            ABS(p.agreeableness_score - tp.agreeableness_score) +
            ABS(p.neuroticism_score - tp.neuroticism_score)
        ) / 5.0
    ) >= similarity_threshold
    ORDER BY similarity_score DESC;
END;
$$ LANGUAGE plpgsql;

-- 创建演示数据插入函数
CREATE OR REPLACE FUNCTION insert_demo_data() RETURNS void AS $$
DECLARE
    demo_user_id UUID;
    demo_personality_id UUID;
BEGIN
    -- 检查是否已存在演示数据
    IF EXISTS (SELECT 1 FROM users WHERE username = 'demo') THEN
        RETURN;
    END IF;

    -- 创建演示用户
    INSERT INTO users (username, email, hashed_password)
    VALUES ('demo', '<EMAIL>', 'demo123')
    RETURNING user_id INTO demo_user_id;

    -- 创建演示人格档案
    INSERT INTO personality_profiles (
        user_id, target_name, description,
        openness_score, conscientiousness_score, extraversion_score,
        agreeableness_score, neuroticism_score,
        decision_making_speed, risk_tolerance,
        average_response_length, vocabulary_complexity, emotional_expressiveness
    )
    VALUES (
        demo_user_id, '演示人格', '这是一个演示用的人格档案',
        0.7, 0.6, 0.8, 0.9, 0.3,
        0.6, 0.4,
        150.0, 0.7, 0.8
    )
    RETURNING profile_id INTO demo_personality_id;

    -- 插入一些演示实体
    INSERT INTO entities (personality_id, name, entity_type, relationship_type, emotional_valence, importance_score)
    VALUES 
        (demo_personality_id, '家人', 'person', 'family', 0.9, 0.95),
        (demo_personality_id, '朋友', 'person', 'friend', 0.8, 0.8),
        (demo_personality_id, '工作', 'concept', 'professional', 0.6, 0.7);

    -- 插入一些演示信念
    INSERT INTO beliefs (personality_id, statement, belief_category, conviction_strength, flexibility_score)
    VALUES 
        (demo_personality_id, '诚实是最重要的品质', 'moral', 0.9, 0.2),
        (demo_personality_id, '努力工作会有回报', 'personal', 0.8, 0.4),
        (demo_personality_id, '家庭比事业更重要', 'personal', 0.7, 0.6);

    -- 插入一些演示事件
    INSERT INTO events (personality_id, title, age_at_event, life_stage, event_type, emotional_impact, centrality_score, memory_vividness)
    VALUES 
        (demo_personality_id, '大学毕业', 22, 'young_adult', 'achievement', 0.8, 0.7, 0.9),
        (demo_personality_id, '第一份工作', 23, 'young_adult', 'professional', 0.6, 0.6, 0.8),
        (demo_personality_id, '结婚', 28, 'adult', 'relationship', 0.9, 0.9, 0.95);

END;
$$ LANGUAGE plpgsql;

-- 数据库初始化完成后的通知
DO $$
BEGIN
    RAISE NOTICE '人格复刻系统数据库初始化完成';
    RAISE NOTICE '- 已创建必要的扩展和类型';
    RAISE NOTICE '- 已定义索引创建函数';
    RAISE NOTICE '- 已创建工具函数';
    RAISE NOTICE '- 准备插入演示数据';
END $$;
