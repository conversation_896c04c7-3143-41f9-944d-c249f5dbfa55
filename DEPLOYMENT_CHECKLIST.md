# 部署检查清单

## 🚀 快速部署指南

### 前置要求
- [ ] Docker Desktop 已安装并运行
- [ ] Python 3.10+ 已安装
- [ ] Node.js 16+ 已安装
- [ ] Git 已安装

### 一键启动（推荐）
```bash
python quick_start.py
```

### 手动部署步骤

#### 1. 环境准备
- [ ] 克隆项目到本地
- [ ] 确保所有前置软件已安装

#### 2. 数据库服务
```bash
# 启动所有数据库服务
docker-compose up -d

# 验证服务状态
docker-compose ps
```

预期服务：
- [ ] PostgreSQL (端口 5432)
- [ ] Neo4j (端口 7474, 7687)
- [ ] ChromaDB (端口 8000)
- [ ] Redis (端口 6379)
- [ ] Elasticsearch (端口 9200)

#### 3. 后端配置
```bash
cd backend

# 复制环境变量文件
cp .env.example .env

# 编辑 .env 文件，填入API密钥
# 必需: GEMINI_API_KEY
# 可选: OPENAI_API_KEY

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python init_db.py

# 启动后端服务
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### 4. 前端配置
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

#### 5. 验证部署
```bash
# 运行系统测试
python test_system.py
```

## 🔧 配置说明

### 环境变量配置
编辑 `backend/.env` 文件：

```env
# 必需配置
GEMINI_API_KEY="your-gemini-api-key"

# 可选配置
OPENAI_API_KEY="your-openai-api-key"
SECRET_KEY="your-secret-key"
DEBUG=true
```

### API密钥获取
1. **Gemini API Key**:
   - 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
   - 创建新的API密钥
   - 复制密钥到 `.env` 文件

2. **OpenAI API Key** (可选):
   - 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
   - 创建新的API密钥

## 📊 服务地址

部署成功后，以下服务将可用：

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端界面 | http://localhost:5173 | Vue.js应用 |
| 后端API | http://localhost:8000 | FastAPI服务 |
| API文档 | http://localhost:8000/docs | Swagger文档 |
| Neo4j浏览器 | http://localhost:7474 | 图数据库管理 |
| PostgreSQL | localhost:5432 | 关系数据库 |
| ChromaDB | http://localhost:8000 | 向量数据库 |
| Redis | localhost:6379 | 缓存服务 |

## 🧪 测试账号

系统提供演示账号：
- 用户名: `demo`
- 密码: `demo123`

## 🔍 故障排除

### 常见问题

#### 1. Docker服务启动失败
```bash
# 检查Docker状态
docker --version
docker info

# 重启Docker服务
docker-compose down
docker-compose up -d
```

#### 2. 端口冲突
如果端口被占用，修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "5433:5432"  # 将PostgreSQL端口改为5433
```

#### 3. Python依赖安装失败
```bash
# 升级pip
pip install --upgrade pip

# 清理缓存
pip cache purge

# 重新安装
pip install -r requirements.txt
```

#### 4. Node.js依赖安装失败
```bash
# 清理缓存
npm cache clean --force

# 删除node_modules
rm -rf node_modules package-lock.json

# 重新安装
npm install
```

#### 5. API调用失败
- 检查 `.env` 文件中的API密钥是否正确
- 确认网络连接正常
- 查看后端日志输出

### 日志查看

#### 后端日志
后端服务会在控制台输出详细日志，包括：
- 请求/响应信息
- 错误堆栈
- 数据库操作

#### 数据库日志
```bash
# 查看Docker容器日志
docker-compose logs postgres
docker-compose logs neo4j
docker-compose logs chromadb
```

#### 前端日志
前端错误会在浏览器开发者工具的控制台中显示。

## 🛑 停止服务

### 快速停止
```bash
python stop_services.py
```

### 手动停止
```bash
# 停止数据库服务
docker-compose down

# 停止后端服务 (Ctrl+C)
# 停止前端服务 (Ctrl+C)
```

## 📈 性能优化

### 生产环境配置
1. 设置 `DEBUG=false`
2. 使用生产级数据库配置
3. 配置反向代理 (Nginx)
4. 启用HTTPS
5. 配置监控和日志收集

### 资源要求
- **最小配置**: 4GB RAM, 2 CPU核心
- **推荐配置**: 8GB RAM, 4 CPU核心
- **存储空间**: 至少10GB可用空间

## 🔒 安全注意事项

1. **API密钥安全**:
   - 不要将API密钥提交到版本控制
   - 定期轮换API密钥
   - 使用环境变量管理敏感信息

2. **数据库安全**:
   - 修改默认密码
   - 限制网络访问
   - 启用SSL连接

3. **应用安全**:
   - 配置CORS策略
   - 启用请求限制
   - 实施输入验证

## 📞 技术支持

如遇到问题：
1. 查看本文档的故障排除部分
2. 检查项目的 Issues 页面
3. 运行 `python test_system.py` 进行诊断
4. 提供详细的错误日志和环境信息
