#!/bin/bash

echo "========================================"
echo "100% 人格复刻系统启动脚本"
echo "========================================"
echo

# 检查Docker是否运行
echo "[1/5] 检查Docker状态..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker未启动，请先启动Docker"
    exit 1
fi
echo "✅ Docker已就绪"

# 启动数据库服务
echo
echo "[2/5] 启动数据库服务..."
if ! docker-compose up -d; then
    echo "❌ 数据库服务启动失败"
    exit 1
fi
echo "✅ 数据库服务已启动"

# 等待数据库初始化
echo
echo "[3/5] 等待数据库初始化..."
sleep 10
echo "✅ 数据库初始化完成"

# 检查Python环境
echo
echo "[4/5] 检查Python环境..."
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ Python未安装，请先安装Python 3.10+"
    exit 1
fi
echo "✅ Python已就绪"

# 检查Node.js环境
echo
echo "[5/5] 检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js 16+"
    exit 1
fi
echo "✅ Node.js已就绪"

echo
echo "========================================"
echo "🚀 系统启动完成！"
echo "========================================"
echo
echo "📊 服务状态:"
echo "  - PostgreSQL: http://localhost:5432"
echo "  - Neo4j Browser: http://localhost:7474"
echo "  - ChromaDB: http://localhost:8000"
echo "  - Redis: localhost:6379"
echo "  - Elasticsearch: http://localhost:9200"
echo
echo "🔧 下一步操作:"
echo "  1. 配置后端环境变量 (backend/.env)"
echo "  2. 启动后端服务: cd backend && python -m venv venv && source venv/bin/activate && pip install -r requirements.txt && uvicorn main:app --reload"
echo "  3. 启动前端服务: cd frontend && npm install && npm run dev"
echo "  4. 访问系统: http://localhost:5173"
echo
echo "📖 详细说明请查看 README.md"
echo
