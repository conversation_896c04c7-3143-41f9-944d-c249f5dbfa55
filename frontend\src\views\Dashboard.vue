<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>欢迎使用人格复刻系统</h1>
      <p>通过AI技术实现100%的人格复刻与分析</p>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalPersonalities }}</div>
              <div class="stat-label">人格档案</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><ChatDotRound /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalConversations }}</div>
              <div class="stat-label">对话会话</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalPredictions }}</div>
              <div class="stat-label">预测分析</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.averageAccuracy }}%</div>
              <div class="stat-label">平均准确度</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快速开始</span>
            </div>
          </template>
          
          <div class="action-buttons">
            <el-button
              type="primary"
              size="large"
              @click="createPersonality"
            >
              <el-icon><Plus /></el-icon>
              创建人格档案
            </el-button>
            
            <el-button
              type="success"
              size="large"
              @click="startChat"
            >
              <el-icon><ChatDotRound /></el-icon>
              开始对话分析
            </el-button>
            
            <el-button
              type="warning"
              size="large"
              @click="runPrediction"
            >
              <el-icon><TrendCharts /></el-icon>
              运行预测
            </el-button>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
            </div>
          </template>
          
          <div class="recent-activities">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon><component :is="activity.icon" /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ formatTime(activity.time) }}</div>
              </div>
            </div>
            
            <div v-if="recentActivities.length === 0" class="no-activities">
              暂无活动记录
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 系统状态 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
              <el-button type="text" @click="refreshStatus">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="system-status">
            <div class="status-item">
              <span class="status-label">API服务:</span>
              <el-tag :type="systemStatus.api ? 'success' : 'danger'">
                {{ systemStatus.api ? '正常' : '异常' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">数据库:</span>
              <el-tag :type="systemStatus.database ? 'success' : 'danger'">
                {{ systemStatus.database ? '正常' : '异常' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">AI模型:</span>
              <el-tag :type="systemStatus.aiModel ? 'success' : 'danger'">
                {{ systemStatus.aiModel ? '正常' : '异常' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, ChatDotRound, TrendCharts, CircleCheck, Plus, Refresh } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import { apiMethods } from '../utils/api'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const stats = reactive({
  totalPersonalities: 0,
  totalConversations: 0,
  totalPredictions: 0,
  averageAccuracy: 0
})

const recentActivities = ref([])

const systemStatus = reactive({
  api: true,
  database: true,
  aiModel: true
})

// 方法
const createPersonality = () => {
  router.push('/personalities/create')
}

const startChat = () => {
  router.push('/chat')
}

const runPrediction = () => {
  router.push('/prediction')
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN')
}

const refreshStatus = async () => {
  try {
    // 这里可以调用API检查系统状态
    ElMessage.success('状态已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

const loadDashboardData = async () => {
  try {
    // 模拟数据加载
    stats.totalPersonalities = 3
    stats.totalConversations = 15
    stats.totalPredictions = 8
    stats.averageAccuracy = 87
    
    recentActivities.value = [
      {
        id: 1,
        title: '创建了新的人格档案',
        time: new Date(Date.now() - 1000 * 60 * 30),
        icon: 'User'
      },
      {
        id: 2,
        title: '完成了对话分析',
        time: new Date(Date.now() - 1000 * 60 * 60 * 2),
        icon: 'ChatDotRound'
      },
      {
        id: 3,
        title: '运行了预测分析',
        time: new Date(Date.now() - 1000 * 60 * 60 * 4),
        icon: 'TrendCharts'
      }
    ]
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    ElMessage.error('加载数据失败')
  }
}

// 生命周期
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 30px;
  text-align: center;
}

.dashboard-header h1 {
  font-size: 32px;
  color: #2c3e50;
  margin-bottom: 10px;
}

.dashboard-header p {
  font-size: 16px;
  color: #7f8c8d;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 20px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 5px;
}

.quick-actions {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-buttons .el-button {
  justify-content: flex-start;
  height: 50px;
}

.recent-activities {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #667eea;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #2c3e50;
  margin-bottom: 5px;
}

.activity-time {
  font-size: 12px;
  color: #7f8c8d;
}

.no-activities {
  text-align: center;
  color: #7f8c8d;
  padding: 40px 0;
}

.system-status {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-label {
  font-weight: 500;
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }
  
  .dashboard-header h1 {
    font-size: 24px;
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .system-status {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
