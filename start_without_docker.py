#!/usr/bin/env python3
"""
无Docker启动脚本 - 仅启动后端和前端进行基本测试
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def setup_backend():
    """设置后端环境"""
    print("🔄 设置后端环境...")
    backend_dir = Path('backend')
    
    # 检查虚拟环境
    venv_dir = backend_dir / 'venv'
    if not venv_dir.exists():
        print("🔄 创建Python虚拟环境...")
        try:
            subprocess.run([sys.executable, '-m', 'venv', str(venv_dir)], check=True)
            print("✅ 虚拟环境创建成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 创建虚拟环境失败: {e}")
            return False, None
    
    # 确定Python路径
    if os.name == 'nt':  # Windows
        python_path = venv_dir / 'Scripts' / 'python.exe'
        pip_path = venv_dir / 'Scripts' / 'pip.exe'
    else:  # Unix/Linux/macOS
        python_path = venv_dir / 'bin' / 'python'
        pip_path = venv_dir / 'bin' / 'pip'
    
    # 安装依赖
    requirements_file = backend_dir / 'requirements.txt'
    if requirements_file.exists():
        print("🔄 安装Python依赖...")
        try:
            subprocess.run([str(pip_path), 'install', '-r', str(requirements_file)], check=True)
            print("✅ Python依赖安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装Python依赖失败: {e}")
            return False, None
    
    # 设置环境变量
    env_file = backend_dir / '.env'
    env_example = backend_dir / '.env.example'
    
    if not env_file.exists() and env_example.exists():
        print("🔄 创建环境变量文件...")
        try:
            with open(env_example, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修改为内存数据库配置（无需Docker）
            content = content.replace(
                'DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/personality_clone_db"',
                'DATABASE_URL="sqlite+aiosqlite:///./personality_clone.db"'
            )
            content = content.replace(
                'REDIS_URL="redis://localhost:6380"',
                'REDIS_URL="memory://"'
            )
            
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 环境变量文件创建成功")
            print("⚠️  使用SQLite数据库（演示模式）")
        except Exception as e:
            print(f"❌ 创建环境变量文件失败: {e}")
    
    return True, python_path

def start_backend_service(python_path):
    """启动后端服务"""
    print("🚀 启动后端服务...")
    backend_dir = Path('backend')
    
    try:
        print("📍 后端服务将在 http://localhost:8000 启动")
        print("📖 API文档: http://localhost:8000/docs")
        
        # 启动FastAPI服务
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                str(python_path), '-m', 'uvicorn', 'main:app', 
                '--reload', '--host', '0.0.0.0', '--port', '8000'
            ], cwd=backend_dir, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Unix/Linux/macOS
            subprocess.Popen([
                str(python_path), '-m', 'uvicorn', 'main:app', 
                '--reload', '--host', '0.0.0.0', '--port', '8000'
            ], cwd=backend_dir)
        
        print("✅ 后端服务启动命令已执行")
        return True
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return False

def setup_frontend():
    """设置前端环境"""
    print("🔄 设置前端环境...")
    frontend_dir = Path('frontend')
    
    # 检查node_modules
    node_modules = frontend_dir / 'node_modules'
    if not node_modules.exists():
        print("🔄 安装Node.js依赖...")
        try:
            subprocess.run(['npm', 'install'], cwd=frontend_dir, check=True)
            print("✅ Node.js依赖安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装Node.js依赖失败: {e}")
            return False
    
    return True

def start_frontend_service():
    """启动前端服务"""
    print("🚀 启动前端服务...")
    frontend_dir = Path('frontend')
    
    try:
        print("📍 前端服务将在 http://localhost:5173 启动")
        
        # 启动Vue开发服务器
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd=frontend_dir, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Unix/Linux/macOS
            subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd=frontend_dir)
        
        print("✅ 前端服务启动命令已执行")
        return True
    except Exception as e:
        print(f"❌ 启动前端服务失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 人格复刻系统 - 无Docker启动模式")
    print("=" * 60)
    print()
    print("⚠️  注意: 此模式使用简化配置，仅用于基本功能测试")
    print("   - 使用SQLite替代PostgreSQL")
    print("   - 不包含Neo4j图数据库")
    print("   - 不包含ChromaDB向量数据库")
    print("   - 部分高级功能可能不可用")
    print()
    
    # 设置后端
    success, python_path = setup_backend()
    if not success:
        print("❌ 后端环境设置失败")
        return
    
    print()
    
    # 设置前端
    if not setup_frontend():
        print("❌ 前端环境设置失败")
        return
    
    print()
    
    # 启动后端服务
    if not start_backend_service(python_path):
        print("❌ 后端服务启动失败")
        return
    
    # 等待后端启动
    print("⏳ 等待后端服务启动...")
    time.sleep(3)
    
    # 启动前端服务
    if not start_frontend_service():
        print("❌ 前端服务启动失败")
        return
    
    print()
    print("=" * 60)
    print("🎉 系统启动完成！")
    print("=" * 60)
    print()
    print("📊 服务地址:")
    print("  🌐 前端界面: http://localhost:5173")
    print("  🔧 后端API: http://localhost:8000")
    print("  📚 API文档: http://localhost:8000/docs")
    print()
    print("🔑 测试账号:")
    print("  可以注册新账号进行测试")
    print()
    print("⚠️  重要提醒:")
    print("  1. 这是简化版本，仅用于基本功能测试")
    print("  2. 要使用完整功能，请修复Docker环境")
    print("  3. 运行 'python check_docker.py' 检查Docker状态")
    print()
    print("🛑 停止服务:")
    print("  关闭打开的控制台窗口或按 Ctrl+C")

if __name__ == "__main__":
    main()
