# API Keys
GEMINI_API_KEY="YOUR_GEMINI_API_KEY_HERE"
OPENAI_API_KEY="YOUR_OPENAI_API_KEY_HERE"

# Database connections
DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/personality_clone_db"
# 无Docker模式使用: DATABASE_URL="sqlite+aiosqlite:///./personality_clone.db"
NEO4J_URI="bolt://localhost:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="password"
CHROMA_HOST="localhost"
CHROMA_PORT="8001"
REDIS_URL="redis://localhost:6380"
ELASTICSEARCH_URL="http://localhost:9200"

# Security
SECRET_KEY="your-secret-key-here-change-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application settings
DEBUG=true
LOG_LEVEL="INFO"
MAX_CONVERSATION_HISTORY=100
PERSONALITY_ANALYSIS_DEPTH=5

# Voice analysis (optional)
AZURE_SPEECH_KEY=""
AZURE_SPEECH_REGION=""

# Text analysis
SENTIMENT_MODEL="cardiffnlp/twitter-roberta-base-sentiment-latest"
EMOTION_MODEL="j-hartmann/emotion-english-distilroberta-base"
