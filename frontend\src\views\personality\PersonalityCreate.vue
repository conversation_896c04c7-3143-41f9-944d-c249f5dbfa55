<template>
  <div class="personality-create">
    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><Plus /></el-icon>
        <h2>创建人格档案</h2>
        <p>此功能正在开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
</script>

<style scoped>
.personality-create {
  padding: 20px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h2 {
  margin: 20px 0 10px;
}
</style>
