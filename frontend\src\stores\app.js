import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const globalLoading = ref(false)
  const sidebarCollapsed = ref(false)
  const theme = ref(localStorage.getItem('theme') || 'light')
  const language = ref(localStorage.getItem('language') || 'zh-CN')
  const notifications = ref([])
  const appSettings = ref({
    autoSave: true,
    soundEnabled: true,
    animationsEnabled: true,
    compactMode: false
  })

  // 计算属性
  const isDarkTheme = computed(() => theme.value === 'dark')
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read).length
  )

  // 动作
  const setGlobalLoading = (loading) => {
    globalLoading.value = loading
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
  }

  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebarCollapsed', collapsed.toString())
  }

  const setTheme = (newTheme) => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    
    // 更新HTML类名以应用主题
    document.documentElement.className = newTheme === 'dark' ? 'dark' : ''
  }

  const toggleTheme = () => {
    setTheme(theme.value === 'light' ? 'dark' : 'light')
  }

  const setLanguage = (newLanguage) => {
    language.value = newLanguage
    localStorage.setItem('language', newLanguage)
  }

  const addNotification = (notification) => {
    const id = Date.now().toString()
    notifications.value.unshift({
      id,
      timestamp: new Date(),
      read: false,
      ...notification
    })
    
    // 限制通知数量
    if (notifications.value.length > 50) {
      notifications.value = notifications.value.slice(0, 50)
    }
  }

  const markNotificationAsRead = (id) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  const markAllNotificationsAsRead = () => {
    notifications.value.forEach(n => n.read = true)
  }

  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  const updateAppSettings = (newSettings) => {
    appSettings.value = { ...appSettings.value, ...newSettings }
    localStorage.setItem('appSettings', JSON.stringify(appSettings.value))
  }

  const initializeApp = async () => {
    try {
      // 恢复侧边栏状态
      const savedSidebarState = localStorage.getItem('sidebarCollapsed')
      if (savedSidebarState !== null) {
        sidebarCollapsed.value = savedSidebarState === 'true'
      }

      // 恢复应用设置
      const savedSettings = localStorage.getItem('appSettings')
      if (savedSettings) {
        appSettings.value = { ...appSettings.value, ...JSON.parse(savedSettings) }
      }

      // 应用主题
      document.documentElement.className = theme.value === 'dark' ? 'dark' : ''

      // 初始化通知（可以从服务器获取）
      // await loadNotifications()

    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  }

  const showMessage = (message, type = 'info') => {
    addNotification({
      type,
      title: type === 'error' ? '错误' : type === 'success' ? '成功' : '提示',
      message,
      duration: type === 'error' ? 0 : 3000
    })
  }

  const showSuccess = (message) => {
    showMessage(message, 'success')
  }

  const showError = (message) => {
    showMessage(message, 'error')
  }

  const showWarning = (message) => {
    showMessage(message, 'warning')
  }

  const showInfo = (message) => {
    showMessage(message, 'info')
  }

  // 响应式断点
  const breakpoints = ref({
    xs: window.matchMedia('(max-width: 575px)'),
    sm: window.matchMedia('(min-width: 576px) and (max-width: 767px)'),
    md: window.matchMedia('(min-width: 768px) and (max-width: 991px)'),
    lg: window.matchMedia('(min-width: 992px) and (max-width: 1199px)'),
    xl: window.matchMedia('(min-width: 1200px)')
  })

  const isMobile = computed(() => breakpoints.value.xs.matches)
  const isTablet = computed(() => breakpoints.value.sm.matches || breakpoints.value.md.matches)
  const isDesktop = computed(() => breakpoints.value.lg.matches || breakpoints.value.xl.matches)

  return {
    // 状态
    globalLoading,
    sidebarCollapsed,
    theme,
    language,
    notifications,
    appSettings,
    breakpoints,

    // 计算属性
    isDarkTheme,
    unreadNotifications,
    isMobile,
    isTablet,
    isDesktop,

    // 动作
    setGlobalLoading,
    toggleSidebar,
    setSidebarCollapsed,
    setTheme,
    toggleTheme,
    setLanguage,
    addNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    removeNotification,
    clearAllNotifications,
    updateAppSettings,
    initializeApp,
    showMessage,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
})
