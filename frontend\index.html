<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>100% 人格复刻系统</title>
    <meta name="description" content="基于AI的完整人格复刻与分析系统" />
    <style>
      /* 全局样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                     'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 
                     'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 
                     'Noto Color Emoji';
        line-height: 1.6;
        color: #333;
        background-color: #f5f7fa;
      }
      
      #app {
        min-height: 100vh;
      }
      
      /* 加载动画 */
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 18px;
        font-weight: 500;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 初始加载状态 -->
      <div class="loading-container">
        <div>
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载人格复刻系统...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
