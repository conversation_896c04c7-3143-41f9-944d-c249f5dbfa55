<template>
  <header class="app-header">
    <div class="header-left">
      <el-button
        type="text"
        @click="toggleSidebar"
        class="sidebar-toggle"
      >
        <el-icon size="20"><Menu /></el-icon>
      </el-button>
      
      <div class="logo">
        <span class="logo-text">人格复刻系统</span>
      </div>
    </div>
    
    <div class="header-right">
      <!-- 通知 -->
      <el-dropdown trigger="click" class="notification-dropdown">
        <el-button type="text" class="header-button">
          <el-badge :value="unreadCount" :hidden="unreadCount === 0">
            <el-icon size="18"><Bell /></el-icon>
          </el-badge>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <div class="notification-header">
              <span>通知</span>
              <el-button type="text" size="small" @click="markAllAsRead">
                全部已读
              </el-button>
            </div>
            <div class="notification-list">
              <div
                v-for="notification in notifications.slice(0, 5)"
                :key="notification.id"
                class="notification-item"
                :class="{ unread: !notification.read }"
              >
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-message">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatTime(notification.timestamp) }}</div>
                </div>
              </div>
              <div v-if="notifications.length === 0" class="no-notifications">
                暂无通知
              </div>
            </div>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
      <!-- 主题切换 -->
      <el-button
        type="text"
        class="header-button"
        @click="toggleTheme"
      >
        <el-icon size="18">
          <component :is="isDarkTheme ? 'Sunny' : 'Moon'" />
        </el-icon>
      </el-button>
      
      <!-- 用户菜单 -->
      <el-dropdown trigger="click">
        <div class="user-info">
          <el-avatar :size="32" class="user-avatar">
            {{ userInfo.username?.charAt(0).toUpperCase() }}
          </el-avatar>
          <span class="username">{{ userInfo.username }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="goToSettings">
              <el-icon><Setting /></el-icon>
              设置
            </el-dropdown-item>
            <el-dropdown-item divided @click="handleLogout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Menu, Bell, Sunny, Moon, ArrowDown, Setting, SwitchButton
} from '@element-plus/icons-vue'

import { useAuthStore } from '../../stores/auth'
import { useAppStore } from '../../stores/app'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

// 计算属性
const userInfo = computed(() => authStore.userInfo)
const notifications = computed(() => appStore.notifications)
const unreadCount = computed(() => appStore.unreadNotifications)
const isDarkTheme = computed(() => appStore.isDarkTheme)

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const toggleTheme = () => {
  appStore.toggleTheme()
}

const markAllAsRead = () => {
  appStore.markAllNotificationsAsRead()
}

const formatTime = (time) => {
  const now = new Date()
  const notificationTime = new Date(time)
  const diff = now - notificationTime
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

const goToSettings = () => {
  router.push('/settings')
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const result = await authStore.logout()
    if (result.success) {
      router.push('/login')
    }
  } catch (error) {
    // 用户取消
  }
}
</script>

<style scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  margin-right: 15px;
  color: #606266;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-button {
  color: #606266;
  padding: 8px;
}

.header-button:hover {
  color: #409eff;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.username {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.dropdown-icon {
  color: #909399;
  font-size: 12px;
}

/* 通知相关样式 */
.notification-dropdown :deep(.el-dropdown-menu) {
  width: 320px;
  max-height: 400px;
  overflow: hidden;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #2c3e50;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
  border-left: 3px solid #409eff;
}

.notification-content {
  width: 100%;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-time {
  font-size: 11px;
  color: #909399;
}

.no-notifications {
  padding: 40px 16px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 15px;
  }
  
  .username {
    display: none;
  }
  
  .notification-dropdown :deep(.el-dropdown-menu) {
    width: 280px;
  }
}

/* 深色模式 */
.dark .app-header {
  background: #1f2937;
  border-bottom-color: #374151;
}

.dark .logo-text {
  color: #f9fafb;
}

.dark .header-button {
  color: #d1d5db;
}

.dark .header-button:hover {
  color: #60a5fa;
}

.dark .user-info:hover {
  background-color: #374151;
}

.dark .username {
  color: #f9fafb;
}
</style>
