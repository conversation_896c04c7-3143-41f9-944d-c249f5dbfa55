import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import api from '../utils/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userInfo = computed(() => user.value || {})

  // 动作
  const login = async (credentials) => {
    try {
      isLoading.value = true
      
      const response = await api.post('/auth/login', credentials)
      const { token: newToken, user_id, message } = response.data
      
      // 保存认证信息
      token.value = newToken
      user.value = { user_id, username: credentials.username }
      
      // 持久化存储
      localStorage.setItem('token', newToken)
      localStorage.setItem('user', JSON.stringify(user.value))
      
      // 设置API默认header
      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      
      ElMessage.success(message || '登录成功')
      
      return { success: true }
    } catch (error) {
      console.error('登录失败:', error)
      const message = error.response?.data?.detail || '登录失败，请检查用户名和密码'
      ElMessage.error(message)
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData) => {
    try {
      isLoading.value = true
      
      const response = await api.post('/auth/register', userData)
      const { token: newToken, user_id, message } = response.data
      
      // 注册成功后自动登录
      token.value = newToken
      user.value = { user_id, username: userData.username, email: userData.email }
      
      // 持久化存储
      localStorage.setItem('token', newToken)
      localStorage.setItem('user', JSON.stringify(user.value))
      
      // 设置API默认header
      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      
      ElMessage.success(message || '注册成功')
      
      return { success: true }
    } catch (error) {
      console.error('注册失败:', error)
      const message = error.response?.data?.detail || '注册失败，请稍后重试'
      ElMessage.error(message)
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      // 清除本地状态
      user.value = null
      token.value = null
      
      // 清除持久化存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      
      // 清除API默认header
      delete api.defaults.headers.common['Authorization']
      
      ElMessage.success('已安全退出')
      
      return { success: true }
    } catch (error) {
      console.error('退出失败:', error)
      return { success: false }
    }
  }

  const checkAuth = async () => {
    try {
      const storedToken = localStorage.getItem('token')
      const storedUser = localStorage.getItem('user')
      
      if (!storedToken || !storedUser) {
        return false
      }
      
      // 恢复状态
      token.value = storedToken
      user.value = JSON.parse(storedUser)
      
      // 设置API默认header
      api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`
      
      // 验证token有效性（可选）
      // 这里可以调用一个验证接口来确认token是否仍然有效
      
      return true
    } catch (error) {
      console.error('认证检查失败:', error)
      // 清除无效的认证信息
      await logout()
      return false
    }
  }

  const updateUserInfo = (newUserInfo) => {
    user.value = { ...user.value, ...newUserInfo }
    localStorage.setItem('user', JSON.stringify(user.value))
  }

  const refreshToken = async () => {
    try {
      // 这里可以实现token刷新逻辑
      // const response = await api.post('/auth/refresh')
      // const { token: newToken } = response.data
      // token.value = newToken
      // localStorage.setItem('token', newToken)
      // api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      return true
    } catch (error) {
      console.error('Token刷新失败:', error)
      await logout()
      return false
    }
  }

  // 初始化时检查认证状态
  const initAuth = async () => {
    await checkAuth()
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    userInfo,
    
    // 动作
    login,
    register,
    logout,
    checkAuth,
    updateUserInfo,
    refreshToken,
    initAuth
  }
})
