<template>
  <div class="personality-list">
    <div class="page-header">
      <h1>人格档案</h1>
      <el-button type="primary" @click="createPersonality">
        <el-icon><Plus /></el-icon>
        创建新档案
      </el-button>
    </div>
    
    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><User /></el-icon>
        <h2>人格档案管理</h2>
        <p>此功能正在开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Plus, User } from '@element-plus/icons-vue'

const router = useRouter()

const createPersonality = () => {
  router.push('/personalities/create')
}
</script>

<style scoped>
.personality-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h2 {
  margin: 20px 0 10px;
}
</style>
